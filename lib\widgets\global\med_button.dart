import 'package:flutter/material.dart';

class MED<PERSON>utton extends StatelessWidget {
  final Function onPressed;
  final String text;
  const MEDButton({Key? key, required this.onPressed, this.text = ''}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          fixedSize: Size(190, 40),
        ),
        onPressed: () {
          onPressed();
        },
        child: false
            ? const SizedBox(
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
                height: 24,
                width: 24,
              )
            : Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 12.0),
                child: Text(
                  text,
                  style: TextStyle(fontSize: 22),
                ),
              ),
      ),
    );
  }
}
