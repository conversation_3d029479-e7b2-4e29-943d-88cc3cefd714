# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.4.2"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.1"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.1"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.8.2"
  audio_session:
    dependency: transitive
    description:
      name: audio_session
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.21"
  bloc:
    dependency: transitive
    description:
      name: bloc
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.1.4"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  cached_network_image:
    dependency: transitive
    description:
      name: cached_network_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.1"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  camera:
    dependency: "direct main"
    description:
      name: camera
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.8+1"
  camera_android:
    dependency: transitive
    description:
      name: camera_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.8+3"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.11+1"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.5.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.1+6"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  chewie:
    dependency: transitive
    description:
      name: chewie
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.0"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.16.0"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.3+4"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  csslib:
    dependency: transitive
    description:
      name: csslib
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.2"
  cupertino_icons:
    dependency: transitive
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  dbus:
    dependency: transitive
    description:
      name: dbus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.4"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.2"
  device_info_plus_linux:
    dependency: transitive
    description:
      name: device_info_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  device_info_plus_macos:
    dependency: transitive
    description:
      name: device_info_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.3"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.6.1"
  device_info_plus_web:
    dependency: transitive
    description:
      name: device_info_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  device_info_plus_windows:
    dependency: transitive
    description:
      name: device_info_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  dio:
    dependency: "direct main"
    description:
      name: dio
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.6"
  encrypt:
    dependency: "direct main"
    description:
      name: encrypt
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  ffi:
    dependency: transitive
    description:
      name: ffi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.4"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.24.0"
  firebase_core_platform_interface:
    dependency: "direct overridden"
    description:
      name: firebase_core_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.5.1"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.3"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      url: "https://pub.dartlang.org"
    source: hosted
    version: "11.4.1"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.5.4"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.4.4"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_bloc:
    dependency: "direct main"
    description:
      name: flutter_bloc
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.1.6"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.3.1"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      url: "https://pub.dartlang.org"
    source: hosted
    version: "9.9.1"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.14"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.6"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html:
    dependency: "direct main"
    description:
      name: flutter_widget_from_html
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.5"
  flutter_widget_from_html_core:
    dependency: transitive
    description:
      name: flutter_widget_from_html_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.5+3"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.2.5"
  fwfh_cached_network_image:
    dependency: transitive
    description:
      name: fwfh_cached_network_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.0+5"
  fwfh_chewie:
    dependency: transitive
    description:
      name: fwfh_chewie
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.1+2"
  fwfh_just_audio:
    dependency: transitive
    description:
      name: fwfh_just_audio
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.2+2"
  fwfh_selectable_text:
    dependency: transitive
    description:
      name: fwfh_selectable_text
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.3+1"
  fwfh_svg:
    dependency: transitive
    description:
      name: fwfh_svg
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.2+1"
  fwfh_text_style:
    dependency: transitive
    description:
      name: fwfh_text_style
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.22.08"
  fwfh_url_launcher:
    dependency: transitive
    description:
      name: fwfh_url_launcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.1+3"
  fwfh_webview:
    dependency: transitive
    description:
      name: fwfh_webview
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.2+5"
  get_it:
    dependency: transitive
    description:
      name: get_it
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.6.0"
  html:
    dependency: transitive
    description:
      name: html
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.15.3"
  http:
    dependency: "direct main"
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.13.5"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.2"
  image:
    dependency: "direct main"
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.3.0"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.7+1"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.6+6"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.12"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.6+9"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.6.3"
  intl:
    dependency: transitive
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.4"
  just_audio:
    dependency: transitive
    description:
      name: just_audio
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.37"
  just_audio_platform_interface:
    dependency: transitive
    description:
      name: just_audio_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.5.0"
  just_audio_web:
    dependency: transitive
    description:
      name: just_audio_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.9"
  lints:
    dependency: transitive
    description:
      name: lints
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.11"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.4"
  medarchiver_common:
    dependency: "direct main"
    description:
      path: "."
      ref: "v1.0.1.1"
      resolved-ref: "12bbccb9471c9b72e71ce91fd90153d91413bc02"
      url: "https://git.medarchiver.com/flutter/medarchiver_common.git"
    source: git
    version: "1.0.1+1"
  medarchiver_interfaces:
    dependency: "direct main"
    description:
      path: "."
      ref: "*******"
      resolved-ref: "89fe27b99a09ef857c9e111f747273e2bc3f0821"
      url: "https://git.medarchiver.com/flutter/medarchiver_interfaces.git"
    source: git
    version: "0.0.1"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.0"
  nested:
    dependency: transitive
    description:
      name: nested
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  network_info_plus:
    dependency: transitive
    description:
      name: network_info_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.4+1"
  network_info_plus_linux:
    dependency: transitive
    description:
      name: network_info_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.2"
  network_info_plus_macos:
    dependency: transitive
    description:
      name: network_info_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  network_info_plus_platform_interface:
    dependency: transitive
    description:
      name: network_info_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.3"
  network_info_plus_web:
    dependency: transitive
    description:
      name: network_info_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  network_info_plus_windows:
    dependency: transitive
    description:
      name: network_info_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  nm:
    dependency: transitive
    description:
      name: nm
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.0"
  null_vosk_plugin:
    dependency: "direct main"
    description:
      path: "."
      ref: "*******"
      resolved-ref: "2b5ecc7a788fe4ad7e5a8aed156977951df91d45"
      url: "https://git.medarchiver.com/flutter/null_vosk_plugin.git"
    source: git
    version: "0.0.1+0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.2"
  package_info_plus_linux:
    dependency: transitive
    description:
      name: package_info_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  package_info_plus_macos:
    dependency: transitive
    description:
      name: package_info_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  package_info_plus_web:
    dependency: transitive
    description:
      name: package_info_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.6"
  package_info_plus_windows:
    dependency: transitive
    description:
      name: package_info_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.1"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.15"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.27"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.10"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.6"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.0"
  platform:
    dependency: transitive
    description:
      name: platform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.4"
  pointer_interceptor:
    dependency: transitive
    description:
      name: pointer_interceptor
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.3+4"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.7.3"
  process:
    dependency: transitive
    description:
      name: process
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.4"
  provider:
    dependency: "direct main"
    description:
      name: provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.5"
  qr_code_scanner:
    dependency: "direct main"
    description:
      name: qr_code_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  quiver:
    dependency: transitive
    description:
      name: quiver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.2"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.27.7"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.4"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.2"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.2"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.3+1"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.4.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0+3"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.9"
  timezone:
    dependency: transitive
    description:
      name: timezone
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.0"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.2"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.10"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.26"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.18"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.5"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.5"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.16"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.6"
  url_strategy:
    dependency: "direct main"
    description:
      name: url_strategy
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  uuid:
    dependency: transitive
    description:
      name: uuid
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.7"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  video_player:
    dependency: transitive
    description:
      name: video_player
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.6.1"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.4.3"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.9"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.0"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.16"
  wakelock:
    dependency: transitive
    description:
      name: wakelock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.2"
  wakelock_macos:
    dependency: transitive
    description:
      name: wakelock_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.0"
  wakelock_platform_interface:
    dependency: transitive
    description:
      name: wakelock_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.0"
  wakelock_web:
    dependency: transitive
    description:
      name: wakelock_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.0"
  wakelock_windows:
    dependency: transitive
    description:
      name: wakelock_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.8.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.10.4"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.9.5"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.9.5"
  webviewx:
    dependency: "direct main"
    description:
      name: webviewx
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.2"
  win32:
    dependency: transitive
    description:
      name: win32
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.6.1"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0+3"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.0"
sdks:
  dart: ">=2.17.0 <3.0.0"
  flutter: ">=3.0.0"
