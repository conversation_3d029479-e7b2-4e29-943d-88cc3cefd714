import 'dart:convert';

import 'package:flutter/foundation.dart';

class Dialog {
  final List<String> action;
  final String type;
  final String content;
  final String triggerOn;
  final List<Button> buttons;
  Dialog({
    this.action = const [],
    this.type = '',
    this.content = '',
    this.triggerOn = '',
    this.buttons = const [],
  });

  Dialog copyWith({
    List<String>? action,
    String? type,
    String? content,
    String? triggerOn,
    List<Button>? buttons,
  }) {
    return Dialog(
      action: action ?? this.action,
      type: type ?? this.type,
      content: content ?? this.content,
      triggerOn: triggerOn ?? this.triggerOn,
      buttons: buttons ?? this.buttons,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'action': action,
      'type': type,
      'content': content,
      'triggerOn': triggerOn,
      'buttons': buttons.map((x) => x.toMap()).toList(),
    };
  }

  factory Dialog.fromMap(Map<String, dynamic> map) {
    return Dialog(
      action: List<String>.from(map['action']),
      type: map['type'] ?? '',
      content: map['content'] ?? '',
      triggerOn: map['triggerOn'] ?? '',
      buttons: List<Button>.from(map['buttons']?.map((x) => Button.fromMap(x))),
    );
  }

  String toJson() => json.encode(toMap());

  factory Dialog.fromJson(String source) => Dialog.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Dialog(action: $action, type: $type, content: $content, triggerOn: $triggerOn, buttons: $buttons)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Dialog &&
        listEquals(other.action, action) &&
        other.type == type &&
        other.content == content &&
        other.triggerOn == triggerOn &&
        listEquals(other.buttons, buttons);
  }

  @override
  int get hashCode {
    return action.hashCode ^
        type.hashCode ^
        content.hashCode ^
        triggerOn.hashCode ^
        buttons.hashCode;
  }
}

class Button {
  final String label;
  final String action;
  Button({
    this.label = '',
    this.action = '',
  });

  Button copyWith({
    String? label,
    String? action,
  }) {
    return Button(
      label: label ?? this.label,
      action: action ?? this.action,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'label': label,
      'action': action,
    };
  }

  factory Button.fromMap(Map<String, dynamic> map) {
    return Button(
      label: map['label'] ?? '',
      action: map['action'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Button.fromJson(String source) => Button.fromMap(json.decode(source));

  @override
  String toString() => 'Button(label: $label, action: $action)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Button && other.label == label && other.action == action;
  }

  @override
  int get hashCode => label.hashCode ^ action.hashCode;
}
