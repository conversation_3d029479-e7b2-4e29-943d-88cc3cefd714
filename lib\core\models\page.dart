import 'dart:convert';

import 'package:flutter/foundation.dart';

import '../../widgets/ScansioneBarcode/cam_element.dart';

class MEDPage {
  Map<String, dynamic> data;
  MEDPage({
    this.data = const {},
  });
}

class PageEmpty extends MEDPage {}

class PageLoading extends MEDPage {}

class PageError extends MEDPage {
  String error;
  PageError({
    this.error = '',
  });
}

class PageLoaded extends MEDPage {
  String type;
  PageLoaded({
    this.type = '',
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type,
    };
  }

  factory PageLoaded.fromMap(Map<String, dynamic> map) {
    return PageLoaded(
      type: map['type'],
    );
  }

  String toJson() => json.encode(toMap());

  factory PageLoaded.fromJson(String source) => PageLoaded.fromMap(json.decode(source));

  PageLoaded copyWith({
    String? type,
  }) {
    return PageLoaded(
      type: type ?? this.type,
    );
  }

  @override
  String toString() => 'PageLoaded(type: $type)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is PageLoaded && other.type == type;
  }

  @override
  int get hashCode => type.hashCode;
}
