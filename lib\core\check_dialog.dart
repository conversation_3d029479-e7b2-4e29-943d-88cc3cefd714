import 'package:flutter/material.dart';
import '/widgets/global/med_dialog.dart';

import 'models/dialog.dart' as d;

Future<bool> checkDialogs(BuildContext context, String action,
    List<d.Dialog> dialogs, String trigger) async {
  bool stopFlag = false;
  for (d.Dialog dialog in dialogs) {
    for (String dialogAction in dialog.action) {
      if (dialogAction == action && dialog.triggerOn == trigger) {
        stopFlag = await showMEDdialog(context, dialog);
      }
    }
  }
  return stopFlag;
}
