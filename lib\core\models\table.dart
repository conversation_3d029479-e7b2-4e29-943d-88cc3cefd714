import 'dart:convert';

import 'package:flutter/foundation.dart';

import 'row.dart';

class Table {
  final bool check;
  final List<String> titles;
  final List<int> hiddenTitles;
  final List<Row> rows;
  Table({
    this.check = false,
    this.titles = const [],
    this.hiddenTitles = const [],
    this.rows = const [],
  });

  Table copyWith({
    bool? check,
    List<String>? titles,
    List<int>? hiddenTitles,
    List<Row>? rows,
  }) {
    return Table(
      check: check ?? this.check,
      titles: titles ?? this.titles,
      hiddenTitles: hiddenTitles ?? this.hiddenTitles,
      rows: rows ?? this.rows,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'check': check,
      'titles': titles,
      'hiddenTitles': hiddenTitles,
      'rows': rows.map((x) => x.toMap()).toList(),
    };
  }

  factory Table.fromMap(Map<String, dynamic> map) {
    return Table(
      check: map['check'] ?? false,
      titles: List<String>.from(map['titles']),
      hiddenTitles: List<int>.from(map['hiddenTitles'] ?? []),
      rows: List<Row>.from(map['rows']?.map((x) => Row.fromMap(x))),
    );
  }

  String toJson() => json.encode(toMap());

  factory Table.fromJson(String source) => Table.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Table(check: $check, titles: $titles, hiddenTitles: $hiddenTitles, rows: $rows)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Table &&
        other.check == check &&
        listEquals(other.titles, titles) &&
        listEquals(other.hiddenTitles, hiddenTitles) &&
        listEquals(other.rows, rows);
  }

  @override
  int get hashCode {
    return check.hashCode ^ titles.hashCode ^ hiddenTitles.hashCode ^ rows.hashCode;
  }
}
