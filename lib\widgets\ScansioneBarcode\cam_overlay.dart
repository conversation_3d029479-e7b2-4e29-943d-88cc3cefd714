import 'dart:developer';

import 'package:flutter/material.dart';
import '/core/models/overlay.dart';

class CamOverlay extends StatelessWidget {
  final OverlayData data;
  final double cameraHeight;
  const CamOverlay({Key? key, required this.data, this.cameraHeight = 1.00})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: <PERSON><PERSON>(data, cameraHeight),
    );
  }
}

class Roi extends CustomPainter {
  final OverlayData data;
  final double cameraHeight;
  Roi(this.data, this.cameraHeight);
  @override
  void paint(Canvas canvas, Size size) {
    log(cameraHeight.toString());
    double diff = size.height / cameraHeight;
    log(diff.toString());
    final paint = Paint()..color = Colors.black38;
    final bg = Path()..addRect(Rect.fromLTWH(0, 0, size.width, size.height));
    final roi = Path()
      ..addRect(Rect.fromLTWH(
        data.x * diff,
        data.y * diff,
        data.width * diff,
        data.height * diff,
      ))
      ..close();
    canvas.drawPath(Path.combine(PathOperation.difference, bg, roi), paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
