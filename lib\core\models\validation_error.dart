import 'dart:convert';

class ValidationError {
  final String notNull;
  final String integer;
  final String minValue;
  final String maxValue;
  final String accuracy;
  ValidationError({
    this.notNull = '',
    this.integer = '',
    this.minValue = '',
    this.maxValue = '',
    this.accuracy = '',
  });

  ValidationError copyWith({
    String? notNull,
    String? integer,
    String? minValue,
    String? maxValue,
    String? accuracy,
  }) {
    return ValidationError(
      notNull: notNull ?? this.notNull,
      integer: integer ?? this.integer,
      minValue: minValue ?? this.minValue,
      maxValue: maxValue ?? this.maxValue,
      accuracy: accuracy ?? this.accuracy,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'notNull': notNull,
      'integer': integer,
      'minValue': minValue,
      'maxValue': maxValue,
      'accuracy': accuracy,
    };
  }

  factory ValidationError.fromMap(Map<String, dynamic> map) {
    return ValidationError(
      notNull: map['notNull'] ?? '',
      integer: map['integer'] ?? '',
      minValue: map['minValue'] ?? '',
      maxValue: map['maxValue'] ?? '',
      accuracy: map['accuracy'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory ValidationError.fromJson(String source) => ValidationError.fromMap(json.decode(source));

  @override
  String toString() {
    return 'ValidationError(notNull: $notNull, integer: $integer, minValue: $minValue, maxValue: $maxValue, accuracy: $accuracy)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ValidationError &&
        other.notNull == notNull &&
        other.integer == integer &&
        other.minValue == minValue &&
        other.maxValue == maxValue &&
        other.accuracy == accuracy;
  }

  @override
  int get hashCode {
    return notNull.hashCode ^
        integer.hashCode ^
        minValue.hashCode ^
        maxValue.hashCode ^
        accuracy.hashCode;
  }
}
