name: preparazione_assistita_farmaci
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.5+0

environment:
  sdk: '>=2.12.0 <3.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_widget_from_html: ^0.8.5
  url_strategy: ^0.2.0
  qr_code_scanner: ^1.0.0
  flutter_bloc: ^8.0.0
  dio: ^4.0.0
  #shared_preferences: ^2.0.12
  http: ^0.13.3
  camera: ^0.9.4+5
  image: ^3.1.0
  fluttertoast: ^8.0.8
  encrypt: ^5.0.1
  url_launcher: ^6.0.17
  webviewx: ^0.2.1
  provider: ^6.0.2
  # medarchiver_vosk_plugin:
  #   path: '../medarchiver_vosk_plugin/'
  medarchiver_common:
    # path: '../medarchiver_common/'
    git:
      url: https://git.medarchiver.com/flutter/medarchiver_common.git
      ref: v1.0.1.1
  null_vosk_plugin:
    git:
      url: https://git.medarchiver.com/flutter/null_vosk_plugin.git
      ref: 0.0.1.0
  medarchiver_interfaces:
    #path: '../medarchiver_interfaces/'
    git:
      url: https://git.medarchiver.com/flutter/medarchiver_interfaces.git
      ref: 0.0.0.1
  image_picker: ^0.8.3
  flutter_local_notifications: ^9.5.3+1
  firebase_core: ^1.24.0
  firebase_messaging: 11.4.1
    # ref: v1.0.0.7
  device_info_plus: 3.2.2

dependency_overrides:
  firebase_core_platform_interface: 4.5.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^1.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/config.json
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
