import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:medarchiver_common/interfaces.dart';
import 'package:medarchiver_common/services.dart';
import 'package:medarchiver_interfaces/interfaces.dart';

import 'package:null_vosk_plugin/null_vosk_plugin.dart';

ILogger logger = serviceLocator<ILogger>();

///Register on GetIt both commonServices and localServices (used by app).
void setupServices() {
  setupCommonServices();
  serviceLocator.registerSingleton<IVoskPlugin>(
    NullVoskPlugin(),
  );
}

///Init all async services.
///
/// * IVoskPlugin -> voice recognition plugin.
/// * InitCommonService -> all common libs
Future initServices() async {
  IVoskPlugin voskPlugin = serviceLocator<IVoskPlugin>();
  voskPlugin.Init();
  await voskPlugin.initModel();
  voskPlugin.recognizeNumbers = false;
  voskPlugin.stop();
  try {
    String configJson = await rootBundle.loadString('assets/config.json');
    await initCommonServices(configJson);
    serviceLocator<IOAuth2Api>().setUrl = () => serviceLocator<IKeyValueStorage>()['OAUTH_URL'];
    //SMRACH: Quando la common sarà aggiornata togliere commento per auto complete.
    /*serviceLocator<IInitializeViewModel>().onConfigLoaded = () async {
      serviceLocator<IInitializeViewModel>().applyConfig();
    };*/
  } catch (e) {
    logger.error(e.toString());
  }
}
