import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' as s;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:medarchiver_interfaces/interfaces.dart';

import 'package:intl/intl.dart';
import 'package:null_vosk_plugin/null_vosk_plugin.dart';

import '/core/models/action.dart';
import '/core/models/review_data.dart';
import '/core/models/text_input.dart';
import '/core/models/validation_data.dart';
import '/core/models/dialog.dart' as d;

import '/widgets/global/action_button.dart';
import '/widgets/MEDScaffold/header.dart';
import '/widgets/MEDScaffold/med_scaffold.dart';
import '/widgets/global/med_html_widget.dart';

class RevisioneDati extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(String, String, List<d.Dialog>) submit;
  const RevisioneDati({Key? key, required this.data, required this.submit}) : super(key: key);

  @override
  State<RevisioneDati> createState() => _RevisioneDatiState();
}

class _RevisioneDatiState extends State<RevisioneDati> {
  late final ReviewData data;
  List<String> errors = const [];
  List<int> errorIds = const [];

  @override
  void initState() {
    data = ReviewData.fromMap(widget.data);
    super.initState();
    IVoskPlugin voskPlugin = context.read<IVoskPlugin>();
    Map<String, AsyncCallback> commands = {};
    for (MEDAction action in data.actions) {
      commands.addAll({
        action.label.toLowerCase(): () async {
          widget.submit(action.action, selectedOption(), data.dialogs);
        }
      });
    }
    voskPlugin.setUserCommandsMapping = commands;
    voskPlugin.startWithGrammar(["[unk]"]);
  }

  @override
  Widget build(BuildContext context) {
    return MEDScaffold(
      header: Header(
        leadingIcon: data.header.leading.isEmpty ? null : Image.network(data.header.leading),
        title: HtmlWidget(data.header.title),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: MedHtmlWidget(
                  html: data.htmlElement,
                  tags: const ['linkedaction'],
                  callback: (val) => widget.submit(val, selectedOption(), data.dialogs)),
            ),
            if (data.textInputs.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                child: Column(
                  children: [
                    for (TextInput item in data.textInputs)
                      item.hidden
                          ? Container()
                          : Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                MedHtmlWidget(
                                    html: item.label,
                                    tags: const ['linkedaction'],
                                    callback: (val) =>
                                        widget.submit(val, selectedOption(), data.dialogs)),
                                Flexible(
                                  child: Padding(
                                    padding: const EdgeInsets.fromLTRB(12, 12, 0, 12),
                                    child: Builder(builder: (context) {
                                      if (item.type == 'date') {
                                        return OutlinedButton(
                                          onPressed: () async {
                                            DateTime? newDate = await showDatePicker(
                                              context: context,
                                              firstDate: DateTime.fromMillisecondsSinceEpoch(
                                                  item.validationData.minValue.floor() * 1000),
                                              initialDate: DateTime.fromMillisecondsSinceEpoch(
                                                  item.validationData.minValue.floor() * 1000),
                                              lastDate: DateTime.fromMillisecondsSinceEpoch(
                                                  item.validationData.maxValue.floor() * 1000),
                                            );
                                            if (newDate != null) {
                                              setState(() {
                                                item.controller.text =
                                                    (newDate.millisecondsSinceEpoch / 1000)
                                                        .floor()
                                                        .toString();
                                                // item.controller = TextEditingController(text: newDate)
                                              });
                                            }
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.all(16.0),
                                            child: Text(
                                              DateFormat('dd-MM-yyyy').format(
                                                  DateTime.fromMillisecondsSinceEpoch(
                                                      int.parse(item.controller.text) * 1000)),
                                              textAlign: TextAlign.left,
                                            ),
                                          ),
                                          style: OutlinedButton.styleFrom(
                                            minimumSize: const Size(double.infinity, 0),
                                            primary: Colors.black87,
                                            side: const BorderSide(color: Colors.grey),
                                            alignment: Alignment.centerLeft,
                                          ),
                                        );
                                      }
                                      Map<String, dynamic> type =
                                          resolveType(item.type, item.validationData);
                                      return TextField(
                                        inputFormatters: type['formatters'],
                                        keyboardType: type['keyboard'],
                                        controller: item.controller,
                                        decoration: InputDecoration(
                                          errorText: item.hasError ? item.error : null,
                                          border: const OutlineInputBorder(),
                                        ),
                                      );
                                    }),
                                  ),
                                )
                              ],
                            ),
                  ],
                ),
              ),
          ],
        ),
      ),
      bottom: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Wrap(
          alignment: WrapAlignment.spaceAround,
          spacing: MediaQuery.of(context).size.width * .05,
          runSpacing: 12,
          children: [
            for (MEDAction action in data.actions)
              ActionButton(
                action,
                onPressed: () {
                  if (!action.validationChecks) {
                    if (formIsValid()) {
                      widget.submit(action.action, selectedOption(), data.dialogs);
                    }
                  }
                },
              ),
          ],
        ),
      ),
    );
  }

  String selectedOption() {
    String result = '';
    for (TextInput textInput in data.textInputs) {
      result += textInput.id;
      result += '=';
      result += textInput.controller.text;
      if (textInput != data.textInputs.last) {
        result += '|';
      }
    }
    return result;
  }

  bool formIsValid() {
    bool isValid = true;
    for (TextInput textInput in data.textInputs) {
      textInput.hasError = false;
      textInput.error = '';
      ValidationData rules = textInput.validationData;
      // null validation
      String value = textInput.controller.text.trim();

      if (rules.notNull == true && textInput.controller.text.isEmpty) {
        textInput.hasError = true;
        textInput.error = textInput.validationError.notNull;
        isValid = false;
      }
      // int validation
      if (rules.integer) {
        if (int.tryParse(value) == null) {
          textInput.error = textInput.validationError.integer;
          textInput.hasError = true;
          isValid = false;
        }
      }

      //range validation
      if (rules.maxValue > rules.minValue) {
        double val = double.tryParse(value) ?? -99999;
        // if (textInput.type == 'date' && val == -99999) {
        //   val = DateTime.parse(value).millisecondsSinceEpoch.toDouble();
        // }
        if (val == -99999) {
          continue;
        }
        if (rules.minValue > val || val > rules.maxValue) {
          textInput.error = val <= rules.minValue
              ? textInput.validationError.minValue
              : textInput.validationError.maxValue;
          textInput.hasError = true;
          isValid = false;
        }
      }
    }
    Future.delayed(Duration(milliseconds: 50), () {
      if (mounted) {
        setState(() {});
      }
    });
    log('isValid: ' + isValid.toString());
    return isValid;
  }

  Map<String, dynamic> resolveType(String type, ValidationData rules) {
    switch (type) {
      case 'number':
        s.TextInputFormatter formatter = s.FilteringTextInputFormatter.allow(RegExp('[0-9]'));
        if (!rules.integer) {
          formatter = s.FilteringTextInputFormatter.allow(
              RegExp(r'(^\d*\.?\d{0,' + rules.accuracy.toString() + '})'));
        }
        return {
          'formatters': [formatter],
          'keyboard': TextInputType.number,
        };
      case 'text_only':
        return {
          'formatters': [
            s.FilteringTextInputFormatter.allow(RegExp("[a-z A-Z á-ú Á-Ú 0-9]")),
          ],
          'keyboard': TextInputType.text,
        };
      case 'phone_number':
        return {
          'formatters': [s.FilteringTextInputFormatter.allow(RegExp("[0-9 + -]"))],
          'keyboard': TextInputType.phone,
        };
      case 'date':
        return {
          'formatters': [s.FilteringTextInputFormatter.allow(RegExp("[0-9 /]"))],
          'keyboard': TextInputType.datetime,
        };
      default:
        return {};
    }
  }
}
