import 'dart:convert';

class Row {
  final String progressivo;
  final String id;
  bool selected;
  final String icon;
  final String text;
  final String info;
  Row({
    this.progressivo = '',
    this.id = '',
    this.selected = false,
    this.icon = '',
    this.text = '',
    this.info = '',
  });

  Row copyWith({
    String? progressivo,
    String? id,
    bool? selected,
    String? icon,
    String? text,
    String? info,
  }) {
    return Row(
      progressivo: progressivo ?? this.progressivo,
      id: id ?? this.id,
      selected: selected ?? this.selected,
      icon: icon ?? this.icon,
      text: text ?? this.text,
      info: info ?? this.info,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'progressivo': progressivo,
      'id': id,
      'selected': selected,
      'icon': icon,
      'text': text,
      'info': info,
    };
  }

  factory Row.fromMap(Map<String, dynamic> map) {
    return Row(
      progressivo: map['progressivo'].toString(),
      id: map['id'].toString(),
      selected: map['preselect'] ?? false,
      icon: map['icon'] ?? '',
      text: map['text'] ?? '',
      info: map['info'].toString(),
    );
  }

  String toJson() => json.encode(toMap());

  factory Row.fromJson(String source) => Row.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Row(progressivo: $progressivo, id: $id, selected: $selected, icon: $icon, text: $text, info: $info)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Row &&
        other.progressivo == progressivo &&
        other.id == id &&
        other.selected == selected &&
        other.icon == icon &&
        other.text == text &&
        other.info == info;
  }

  @override
  int get hashCode {
    return progressivo.hashCode ^
        id.hashCode ^
        selected.hashCode ^
        icon.hashCode ^
        text.hashCode ^
        info.hashCode;
  }
}
