import 'dart:convert';

class ValidationData {
  final bool notNull;
  final bool integer;
  final double minValue;
  final double maxValue;
  final int accuracy;
  ValidationData({
    this.notNull = false,
    this.integer = false,
    this.minValue = 0,
    this.maxValue = 99,
    this.accuracy = 2,
  });

  ValidationData copyWith({
    bool? notNull,
    bool? integer,
    double? minValue,
    double? maxValue,
    int? accuracy,
  }) {
    return ValidationData(
      notNull: notNull ?? this.notNull,
      integer: integer ?? this.integer,
      minValue: minValue ?? this.minValue,
      maxValue: maxValue ?? this.maxValue,
      accuracy: accuracy ?? this.accuracy,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'notNull': notNull,
      'integer': integer,
      'minValue': minValue,
      'maxValue': maxValue,
      'accuracy': accuracy,
    };
  }

  factory ValidationData.fromMap(Map<String, dynamic> map) {
    return ValidationData(
      notNull: map['notNull'] ?? false,
      integer: map['integer'] ?? false,
      minValue: map['minValue']?.toDouble() ?? 0.0,
      maxValue: map['maxValue']?.toDouble() ?? 0.0,
      accuracy: map['accuracy']?.toInt() ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory ValidationData.fromJson(String source) => ValidationData.fromMap(json.decode(source));

  @override
  String toString() {
    return 'ValidationData(notNull: $notNull, integer: $integer, minValue: $minValue, maxValue: $maxValue, accuracy: $accuracy)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ValidationData &&
        other.notNull == notNull &&
        other.integer == integer &&
        other.minValue == minValue &&
        other.maxValue == maxValue &&
        other.accuracy == accuracy;
  }

  @override
  int get hashCode {
    return notNull.hashCode ^
        integer.hashCode ^
        minValue.hashCode ^
        maxValue.hashCode ^
        accuracy.hashCode;
  }
}
