import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:medarchiver_common/services.dart';
import '/widgets/MEDScaffold/header.dart';
import '/widgets/MEDScaffold/med_scaffold.dart';

class PaginaBilancia extends StatelessWidget {
  const PaginaBilancia({Key? key}) : super(key: key);

  final String urlLogo =
      'https://images.squarespace-cdn.com/content/v1/56b92ee81d07c0ddd1cafef9/1462362543730-BKP2QS9RW131TLJ7W8CP/image-asset.png?format=1000w';
  final String titleHtml =
      '<h1 style="text-align: center; color: purple">Bilancia</h1>';
  final String textBox1 = ''' 
<p style="color: purple; text-align: center">1. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.</p>		 
  ''';
  final String textBox2 = ''' 
<p style="color: purple; text-align: center">2. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.</p>		 
  ''';

  @override
  Widget build(BuildContext context) {
    return MEDScaffold(
      header: Header(
        leadingIcon: Image.network(urlLogo),
        title: HtmlWidget(titleHtml),
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Flexible(child: HtmlWidget(textBox1)),
          Expanded(
            child: Center(child: Text(localeString('OUTPUT BILANCIA'))),
          ),
          Flexible(child: HtmlWidget(textBox2)),
        ],
      ),
      bottom: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Wrap(
          alignment: WrapAlignment.spaceAround,
          spacing: MediaQuery.of(context).size.width * .05,
          runSpacing: 12,
          children: [
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localeString('INDIETRO')),
            ),
          ],
        ),
      ),
    );
  }
}
