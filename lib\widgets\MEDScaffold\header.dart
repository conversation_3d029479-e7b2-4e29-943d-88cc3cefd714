import 'dart:math';

import 'package:flutter/material.dart';

class Header extends StatelessWidget {
  final Widget? leadingIcon;
  final Widget title;
  Header({Key? key, this.leadingIcon, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (leadingIcon != null)
          SizedBox(
            // width: min(MediaQuery.of(context).size.width * 1, 75),
            height: 75,
            width: 75,
            child: leadingIcon,
          ),
        Expanded(child: Center(child: title))
      ],
    );
  }
}
