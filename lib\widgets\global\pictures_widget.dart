import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class PicturesDialog extends StatefulWidget {
  final int number;
  const PicturesDialog(this.number, {Key? key}) : super(key: key);

  @override
  State<PicturesDialog> createState() => _PicturesDialogState();
}

class _PicturesDialogState extends State<PicturesDialog> {
  late CameraController controller;
  late List<CameraDescription> _cameras;
  bool isLoading = true;
  late List<XFile?> files;
  late List<String> listId;
  int currentIndex = 0;
  int retakeIndex = -1;

  @override
  void initState() {
    files = List.generate(widget.number, (index) => null);
    listId = List.generate(widget.number, (index) => '');
    super.initState();
    asyncInitState();
  }

  asyncInitState() async {
    _cameras = await availableCameras();
    controller = CameraController(_cameras[0], ResolutionPreset.max);
    controller.initialize().then((_) {
      if (!mounted) {
        return;
      }
      isLoading = false;
      setState(() {});
    }).catchError((Object e) {
      if (e is CameraException) {
        switch (e.code) {
          case 'CameraAccessDenied':
            print('User denied camera access.');
            break;
          default:
            print('Handle other errors.');
            break;
        }
      }
    });
  }

  void addPicture(XFile file) async {
    log(file.name);
    files[currentIndex] = file;
    currentIndex++;
    log(files.toString());
    if (retakeIndex != -1) {
      currentIndex = retakeIndex;
      retakeIndex = -1;
    }
    setState(() {});
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  void retakePicture() {
    retakeIndex = currentIndex;
    currentIndex = currentIndex - widget.number;
    setState(() {});
  }

  void confirmPicture() async {
    final currentfile = base64Encode(await files[currentIndex - widget.number]!.readAsBytes());
    final res = await Dio().post(
      'http://*********:3000/medcheck/api/ATG/V2/saveImage',
      data: {
        'image': currentfile,
        'deviceId': '000000',
        'processId': 12345,
      },
    );
    listId[currentIndex - widget.number] = res.data['statusDescription']['id'].toString();
    setState(() {
      currentIndex += 1;
    });
    if (currentIndex == widget.number * 2) {
      // final List<String> idList = files.map((e) => e!.name).toList();
      log(listId.toString());
      Navigator.pop(context, listId);
    }
  }

  @override
  Widget build(BuildContext context) {
    log(currentIndex.toString() + ' - ' + retakeIndex.toString());
    if (isLoading) {
      return Container();
    }
    if (currentIndex < widget.number) {
      return Dialog(
        insetPadding: EdgeInsets.zero,
        child: Stack(
          children: [
            CameraPreview(controller),
            Positioned.fill(
                child: Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 36.0),
                child: SafeArea(
                  child: FloatingActionButton(
                    onPressed: () async {
                      final file = await controller.takePicture();
                      addPicture(file);
                    },
                    child: const Icon(Icons.camera_alt),
                  ),
                ),
              ),
            )),
            Positioned.fill(
                child: Align(
              alignment: Alignment.bottomLeft,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 36.0, left: 8),
                child: SafeArea(
                    child: FloatingActionButton(
                  child: const Icon(Icons.image_search),
                  onPressed: () async {
                    final XFile? image = await ImagePicker().pickImage(source: ImageSource.gallery);
                    if (image == null) {
                      return;
                    }
                    addPicture(image);
                  },
                )),
              ),
            ))
          ],
        ),
      );
    }
    if (currentIndex < widget.number * 2) {
      return Dialog(
        child: Stack(
          children: [
            Image.file(
              File(files[currentIndex - widget.number]!.path),
              fit: BoxFit.fitHeight,
            ),
            Positioned.fill(
              child: Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      FloatingActionButton(
                          onPressed: () => retakePicture(),
                          child: Icon(Icons.switch_camera_rounded)),
                      if (currentIndex >= widget.number)
                        Text('${(currentIndex - widget.number) + 1}/${widget.number}'),
                      FloatingActionButton(
                          onPressed: () => confirmPicture(), child: Icon(Icons.arrow_forward)),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }
    return const Dialog(
      child: SizedBox(
        height: 75,
        width: 75,
        child: Center(
          child: SizedBox(
            height: 50,
            width: 50,
            child: CircularProgressIndicator(),
          ),
        ),
      ),
    );
  }
}
