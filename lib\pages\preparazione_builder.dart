import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medarchiver_common/interfaces.dart';
import 'package:medarchiver_common/services.dart';
import 'package:preparazione_assistita_farmaci/widgets/global/med_button.dart';

import '/core/core.dart';
import '/core/models/page.dart';
import '/pages/lista_selezionabile.dart';
import '/pages/revisione_dati.dart';
import '/pages/scansione_barcode.dart';
import '/core/models/dialog.dart' as d;

class PreparazioneBuilder extends StatefulWidget {
  final String notificationId;
  final String pageNumber;
  final bool isNewPage;
  const PreparazioneBuilder({
    Key? key,
    required this.notificationId,
    required this.pageNumber,
    this.isNewPage = false,
  }) : super(key: key);

  @override
  State<PreparazioneBuilder> createState() => _PreparazioneBuilderState();
}

class _PreparazioneBuilderState extends State<PreparazioneBuilder> {
  late bool isNewPage;
  final ILogger logger = serviceLocator<ILogger>()..baseClassName = "PreparazioneBuilder";

  @override
  void initState() {
    super.initState();
    isNewPage = widget.isNewPage;
    logger.debug("initState isNewPage=$isNewPage", methodName: 'initState');
  }

  @override
  Widget build(BuildContext context) {
    PageCubit cubit = BlocProvider.of<PageCubit>(context, listen: true);
    return Scaffold(
      body: Builder(
        builder: (context) {
          if (cubit.state is PageError) {
            PageError errorPage = cubit.state as PageError;
            return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Center(child: Text(errorPage.error)),
                  MEDButton(
                    onPressed: () => cubit.getPage(widget.notificationId, widget.pageNumber),
                    text: 'Retry',
                  )
                ]);
          }
          if (cubit.state is PageLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          if (cubit.state is PageEmpty || isNewPage == true) {
            isNewPage = false;
            cubit.getPage(widget.notificationId, widget.pageNumber);
          }
          if (cubit.state is PageLoaded) {
            PageLoaded page = cubit.state as PageLoaded;
            switch (page.type) {
              case 'scanner':
                return ScansioneBarcode(
                  data: page.data,
                  submit: (action, selectedOptions, dialogs) => cubit.postPage(action,
                      selectedOptions, widget.notificationId, widget.pageNumber, context, dialogs),
                );
              case 'list':
                return ListaSelezionabile(
                  data: page.data,
                  submit: (action, selectedOptions, dialogs) => cubit.postPage(
                    action,
                    selectedOptions,
                    widget.notificationId,
                    widget.pageNumber,
                    context,
                    dialogs,
                  ),
                );
              case 'review':
                return RevisioneDati(
                  data: page.data,
                  submit: (action, selectedOptions, dialogs) => cubit.postPage(
                    action,
                    selectedOptions,
                    widget.notificationId,
                    widget.pageNumber,
                    context,
                    dialogs,
                  ),
                );
            }
          }
          return Container();
        },
      ),
    );
  }

  logout(BuildContext context) async {
    //SharedPreferences spm = await SharedPreferences.getInstance();
    //spm.clear();
    await serviceLocator<IOAuth2Api>().logout();
    while (Navigator.canPop(context)) {
      Navigator.pop(context);
      await Navigator.pushReplacementNamed(context, '/');
    }
  }
}
