import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';

import 'package:camera/camera.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:fluttertoast/fluttertoast.dart';

import 'package:image/image.dart' as imglib;
import 'package:medarchiver_common/interfaces.dart';
import 'package:medarchiver_common/services.dart';
import '/core/models/overlay.dart';
import '/widgets/ScansioneBarcode/cam_overlay.dart';

class ObjectRecognition extends StatefulWidget {
  final String text;
  final String url;
  final int cameraId;
  final Function onValid;
  const ObjectRecognition(
      {Key? key,
      required this.text,
      required this.url,
      required this.onValid,
      this.cameraId = 0})
      : super(key: key);

  @override
  _ObjectRecognitionState createState() => _ObjectRecognitionState();
}

class _ObjectRecognitionState extends State<ObjectRecognition> {
  late CameraController controller;
  int cameraID = 0;
  List<CameraDescription> cameras = [];
  bool isCameraInitialized = false;
  bool photoIsLoading = false;
  Size screen = const Size(0, 0);
  OverlayData overlay = OverlayData();
  late String text;

  @override
  void initState() {
    text = widget.text;
    cameraID = widget.cameraId;
    initCamera();
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Size tempSize = Size(MediaQuery.of(context).size.width,
        MediaQuery.of(context).size.height * .9);
    // if (screen != tempSize) {
    //   overlay.isValid = false;
    //   getOverlaySize(tempSize);
    // }
    return Container(
      color: Colors.white,
      child: Container(
        color: Colors.black38,
        child: Column(
          children: [
            (cameras.isNotEmpty && overlay.isValid)
                ? Stack(
                    alignment: AlignmentDirectional.center,
                    children: [
                      SizedBox(
                        height: tempSize.height,
                        child: OverflowBox(
                          alignment: Alignment.center,
                          child: FittedBox(
                            fit: BoxFit.fitWidth,
                            child: SizedBox(
                              width: tempSize.width,
                              child: CameraPreview(
                                controller,
                                child: CamOverlay(
                                  data: overlay,
                                  cameraHeight:
                                      controller.value.previewSize!.width,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Positioned.fill(
                      //   child: CamOverlay(data: overlay),
                      // ),
                      Positioned(
                          width: MediaQuery.of(context).size.width - 16,
                          top: 16,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              FloatingActionButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Icon(Icons.arrow_back),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0),
                                  child: Material(
                                    color: Colors.white,
                                    child: Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Text(
                                        widget.text,
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                ),
                              ),
                              FloatingActionButton(
                                onPressed: switchCamera,
                                child: const Icon(Icons.cameraswitch),
                              ),
                            ],
                          )),
                    ],
                  )
                : const Center(
                    child: CircularProgressIndicator(),
                  ),
            Expanded(
              child: Container(
                width: MediaQuery.of(context).size.width,
                color: Colors.white,
                child: Center(
                    child: HtmlWidget(
                  text,
                )),
              ),
            )
          ],
        ),
      ),
    );
  }

  void switchCamera() {
    controller.dispose();
    if (cameraID == 0) {
      cameraID = 1;
    } else {
      cameraID = 0;
    }
    initCamera();
  }

  void initCamera() async {
    cameras = await availableCameras();
    controller = CameraController(cameras[cameraID], ResolutionPreset.max);
    await controller.initialize();
    if (!mounted) return;
    await getOverlaySize(controller.value.previewSize!);
    controller.setFlashMode(FlashMode.off);
    setState(() {
      isCameraInitialized = true;
    });

    // XFile pic = await controller.takePicture();
    bool hasImage = false;
    controller.startImageStream((CameraImage pic) async {
      if (hasImage || !mounted) {
        return;
      }
      hasImage = true;
      // log(pic.planes[0].bytes.toString());
      Uint8List? img = await compute(convertYUV420toImage,
          {'image': pic, 'overlay': overlay, 'cameraID': cameraID});
      if (img == null) {
        Fluttertoast.showToast(msg: 'image processing failed');
        log('image null');
        hasImage = false;
        return;
      }
      // log('image processed');
      // log(widget.url);
      String b64String = const Base64Encoder().convert(img);

      Response res = await Dio().request(widget.url,
          options: Options(contentType: 'application/json', method: 'POST'),
          data: {"image": b64String});
      // Image formattedImg = Image.memory(img);
      // await showDialog(
      //     context: context,
      //     builder: (_) => Dialog(
      //           child: SizedBox(height: 850, width: 300, child: formattedImg),
      //         ));
      // await Future.delayed(const Duration(seconds: 2), () {});
      // log(controller.value.previewSize.toString());
      // log(pic.lensAperture.toString());
      // log(Size(pic.width.toDouble(), pic.height.toDouble()).toString());

      if (res.statusCode != 200) {
        return;
      }
      if (!mounted) return;
      log(res.data.toString());
      switch (res.data['data']['status'].toString()) {
        case '0':
          setState(() {
            text = res.data['data']['statusDescription'].toString();
          });

          // Fluttertoast.showToast(msg: res.data['data']['statusDescription'].toString());
          // log(res.data['data']['statusDescription'].toString());
          break;
        case '1':
          Navigator.pop(context);
          widget.onValid(res.data['data']['statusDescription'].toString());
          return;
        // PageCubit().postPage('confirm', '', notificationId, pageNumber, context);
        case '2':
          Navigator.pop(context);
          Fluttertoast.showToast(
              msg: res.data['data']['statusDescription'].toString());
          break;
        default:
      }
      hasImage = false;
      // log(res.data.toString());
    });
  }

  Future<void> getOverlaySize(Size newSize) async {
    // log('getOverlaySize: ' + newSize.toString());
    IKeyValueStorage appData = serviceLocator<IKeyValueStorage>();
    String apiUrl = appData["API_URL"];
    Response res = await Dio().request(
      '$apiUrl/getROI',
      data: {
        'width': newSize.height, // valori invertiti in
        'height': newSize.width, // controller.value.previewSize
      },
      options: Options(method: 'POST'),
    );
    if (res.statusCode != 200) {
      return;
    }
    // log(res.data['data'].toString());
    setState(() {
      screen = newSize;
      overlay = OverlayData.fromMap(res.data['data']);
    });
  }
}

Future<Uint8List?> convertYUV420toImage(Map map) async {
  int cameraID = map['cameraID'];
  CameraImage image = map['image'];
  OverlayData roi = map['overlay'];
  // log('CAM ID: ' + cameraID.toString());
  try {
    final int width = image.width;
    final int height = image.height;

    imglib.Image img = imglib.Image(width, height);

    // Fill image buffer with plane[0] from YUV420_888
    for (int x = 0; x < width; x++) {
      for (int y = 0; y < height; y++) {
        final pixelColor = image.planes[0].bytes[y * width + x];
        // color: 0x FF  FF  FF  FF
        //           A   B   G   R
        // Calculate pixel color
        img.data[y * width + x] =
            (0xFF << 24) | (pixelColor << 16) | (pixelColor << 8) | pixelColor;
      }
    }

    return imglib.JpegEncoder().encodeImage(imglib.copyCrop(
      imglib.copyRotate(img, 90 + (cameraID == 1 ? 180 : 0)),
      roi.x.floor(),
      roi.y.floor(),
      roi.width.floor(),
      roi.height.floor(),
    )) as Uint8List;
  } catch (e) {
    print(">>>>>>>>>>>> ERROR:" + e.toString());
    return null;
  }
}
