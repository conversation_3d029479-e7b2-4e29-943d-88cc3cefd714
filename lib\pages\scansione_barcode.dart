import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/basic_types.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:medarchiver_interfaces/interfaces.dart';

import 'package:null_vosk_plugin/null_vosk_plugin.dart';
import '/core/core.dart';
import '/core/models/action.dart';
import '/core/models/scansione_data.dart';
import '/widgets/global/action_button.dart';
import 'package:provider/src/provider.dart';
import '/widgets/MEDScaffold/header.dart';
import '/widgets/MEDScaffold/med_scaffold.dart';
import '/widgets/ScansioneBarcode/cam_element.dart';
import '/widgets/global/med_html_widget.dart';
import '/core/models/dialog.dart' as d;

class ScansioneBarcode extends StatefulWidget {
  // final String urlLogo;
  // final String title;
  // final String instructionsHtml;
  // final CamType type;
  final Map<String, dynamic> data;
  final Function(String, String, List<d.Dialog>) submit;

  const ScansioneBarcode({Key? key, required this.data, required this.submit}) : super(key: key);

  @override
  State<ScansioneBarcode> createState() => _ScansioneBarcodeState();
}

class _ScansioneBarcodeState extends State<ScansioneBarcode> {
  late final ScansioneData data;
  @override
  void initState() {
    log(widget.data.toString());

    data = ScansioneData.fromMap(widget.data);
    log(
      data.scannerType == 'objectRecognition' ? widget.data['objRecognitionInfo']['url_ws'] : 'LOG',
    );
    super.initState();
    IVoskPlugin voskPlugin = context.read<IVoskPlugin>();
    Map<String, AsyncCallback> commands = {};
    for (MEDAction action in data.actions) {
      commands.addAll({
        action.label.toLowerCase(): () async {
          widget.submit(action.action, selectedOption(), data.dialogs);
        }
      });
    }
    voskPlugin.setUserCommandsMapping = commands;
    voskPlugin.startWithGrammar(["[unk]"]);
  }

  @override
  Widget build(BuildContext context) {
    return MEDScaffold(
      header: Header(
        leadingIcon: data.header.leading.isEmpty ? null : Image.network(data.header.leading),
        title: HtmlWidget(data.header.title),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            MedHtmlWidget(
                html: data.userInstructions,
                tags: const ['linkedaction'],
                callback: (val) => widget.submit(val, selectedOption(), data.dialogs)),
            CamElement(
              data.scannerType,
              cameraInfo: data.cameraInfo,
              cameraInstructions: data.userInstructionsLight,
              autoCommitAction: widget.data['autoCommitAction'] ?? 'confirm',
              objRecognitionUrl: data.scannerType == 'objectRecognition'
                  ? widget.data['objRecognitionInfo']['url_ws']
                  : '',
              onBarcodeFound: (barcode) async {
                if (data.footerHtml == barcode) return;
                setState(() {
                  data.footerHtml = barcode;
                });
                widget.submit(widget.data['autoCommitAction'], selectedOption(), data.dialogs);
                data.footerHtml = '';
              },
            ),
            MedHtmlWidget(
                html: data.footerHtml,
                tags: const ['linkedaction'],
                callback: (val) => widget.submit(val, selectedOption(), data.dialogs)),
          ],
        ),
      ),
      bottom: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Wrap(
          alignment: WrapAlignment.spaceAround,
          spacing: MediaQuery.of(context).size.width * .05,
          runSpacing: 12,
          children: [
            for (MEDAction action in data.actions)
              ActionButton(action,
                  onPressed: () => widget.submit(action.action, selectedOption(), data.dialogs)),
            // ElevatedButton(
            //     onPressed: () => widget.submit(action.action, selectedOption()),
            //     child: Text(action.label)),
          ],
        ),
      ),
    );
  }

  String selectedOption() {
    return data.footerHtml;
  }
}
