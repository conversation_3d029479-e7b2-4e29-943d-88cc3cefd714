import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import '/core/models/dialog.dart' as d;
import '/widgets/global/med_button.dart';

class MEDdialog extends StatelessWidget {
  final d.Dialog dialog;
  final String type;
  final String content;
  final List<Widget> actions;
  const MEDdialog(this.dialog,
      {Key? key, this.type = '', this.content = '', this.actions = const []})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Builder(builder: (context) {
        switch (dialog.type) {
          case 'warning':
            return const Icon(
              Icons.warning_amber_outlined,
              size: 60,
              color: Colors.orange,
            );
          case 'error':
            return const Icon(
              Icons.error_outline_outlined,
              size: 40,
              color: Colors.red,
            );
          case 'info':
            return const Icon(
              Icons.info_outline_rounded,
              size: 40,
              color: Colors.blue,
            );
          case 'success':
            return const Icon(
              Icons.check_circle_outline_outlined,
              color: Colors.green,
              size: 40,
            );
          default:
            return Container();
        }
      }),
      content: Container(
        constraints: const BoxConstraints(maxWidth: 600),
        child: HtmlWidget(dialog.content),
      ),
      actions: dialog.buttons
          .map((e) => MEDButton(
                onPressed: () {
                  if (e.action == 'cancel') {
                    Navigator.pop(context, true);
                  } else {
                    Navigator.pop(context, false);
                  }
                },
                text: e.label,
              ))
          .toList(),
    );
  }
}

Future<dynamic> showMEDdialog(context, d.Dialog dialog) async {
  return showDialog(
      context: context,
      builder: (_) {
        return MEDdialog(dialog);
      });
}

Future<dynamic> showLoadingDialog(
  context,
) async {
  return showDialog(
      barrierDismissible: false,
      context: context,
      builder: (_) {
        return Dialog(
          insetPadding: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * .3),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: const [
                CircularProgressIndicator(),
                Padding(
                  padding: EdgeInsets.only(top: 24.0),
                  child: Text('LOADING...'),
                ),
              ],
            ),
          ),
        );
      });
}
