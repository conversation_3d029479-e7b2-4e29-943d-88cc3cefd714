import 'package:flutter/material.dart';
import '/core/models/action.dart';

class ActionButton extends StatelessWidget {
  final MEDAction action;
  final Function onPressed;
  ActionButton(final this.action, {Key? key, required this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
        style: ElevatedButton.styleFrom(
            primary: Color.fromRGBO(
                action.buttonColor[0], action.buttonColor[1], action.buttonColor[2], 1)),
        onPressed: () => onPressed(),
        child: Text(
          action.label,
          style: TextStyle(color: action.isBrightColor ? Colors.black87 : Colors.white),
        ));
  }
}
