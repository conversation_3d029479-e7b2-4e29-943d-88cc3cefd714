import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class Videocall extends StatefulWidget {
  final String path;
  const Videocall(this.path, {Key? key}) : super(key: key);

  @override
  State<Videocall> createState() => _VideocallState();
}

class _VideocallState extends State<Videocall> {
  @override
  void initState() {
    callIntent(widget.path);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container();
  }

  void callIntent(String path) async {
    final String url = 'http://stream.medarchvier.com' + path;
    if (await canLaunch(url)) {
      launch(url);
    }
  }
}
