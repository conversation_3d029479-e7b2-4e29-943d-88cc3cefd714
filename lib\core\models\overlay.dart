import 'dart:convert';

class OverlayData {
  bool isValid;
  final double height;
  final double width;
  final double x;
  final double y;
  OverlayData({
    this.isValid = false,
    this.height = 0.0,
    this.width = 0.0,
    this.x = 0.0,
    this.y = 0.0,
  });

  Map<String, dynamic> toMap() {
    return {
      'isValid': isValid,
      'height': height,
      'width': width,
      'x': x,
      'y': y,
    };
  }

  factory OverlayData.fromMap(Map<String, dynamic> map) {
    return OverlayData(
      isValid: true,
      height: map['height']?.toDouble() ?? 0.0,
      width: map['width']?.toDouble() ?? 0.0,
      x: map['x']?.toDouble() ?? 0.0,
      y: map['y']?.toDouble() ?? 0.0,
    );
  }

  String toJson() => json.encode(toMap());

  factory OverlayData.fromJson(String source) => OverlayData.fromMap(json.decode(source));

  OverlayData copyWith({
    bool? isValid,
    double? height,
    double? width,
    double? x,
    double? y,
  }) {
    return OverlayData(
      isValid: isValid ?? this.isValid,
      height: height ?? this.height,
      width: width ?? this.width,
      x: x ?? this.x,
      y: y ?? this.y,
    );
  }

  @override
  String toString() {
    return 'OverlayData(isValid: $isValid, height: $height, width: $width, x: $x, y: $y)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is OverlayData &&
        other.isValid == isValid &&
        other.height == height &&
        other.width == width &&
        other.x == x &&
        other.y == y;
  }

  @override
  int get hashCode {
    return isValid.hashCode ^ height.hashCode ^ width.hashCode ^ x.hashCode ^ y.hashCode;
  }
}
