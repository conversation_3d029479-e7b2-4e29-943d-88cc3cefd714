import 'dart:convert';

import 'package:flutter/foundation.dart';

class Tokens {
  // ignore: non_constant_identifier_names
  final String access_token;
  // ignore: non_constant_identifier_names
  final String token_type;
  final int expires_in;
  final String refresh_token;
  final AccessQuery accessQuery;
  Tokens({
    this.access_token = '',
    this.token_type = '',
    this.expires_in = 0,
    this.refresh_token = '',
    required this.accessQuery,
  });

  Tokens copyWith({
    String? access_token,
    String? token_type,
    int? expires_in,
    String? refresh_token,
    AccessQuery? accessQuery,
  }) {
    return Tokens(
      access_token: access_token ?? this.access_token,
      token_type: token_type ?? this.token_type,
      expires_in: expires_in ?? this.expires_in,
      refresh_token: refresh_token ?? this.refresh_token,
      accessQuery: accessQuery ?? this.accessQuery,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'access_token': access_token,
      'token_type': token_type,
      'expires_in': expires_in,
      'refresh_token': refresh_token,
      'accessQuery': accessQuery.toMap(),
    };
  }

  factory Tokens.fromMap(Map<String, dynamic> map) {
    return Tokens(
      access_token: map['access_token'],
      token_type: map['token_type'],
      expires_in: map['expires_in'],
      refresh_token: map['refresh_token'],
      accessQuery: AccessQuery.fromMap(map['accessQuery']),
    );
  }

  String toJson() => json.encode(toMap());

  factory Tokens.fromJson(String source) => Tokens.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Tokens(access_token: $access_token, token_type: $token_type, expires_in: $expires_in, refresh_token: $refresh_token, accessQuery: $accessQuery)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Tokens &&
        other.access_token == access_token &&
        other.token_type == token_type &&
        other.expires_in == expires_in &&
        other.refresh_token == refresh_token &&
        other.accessQuery == accessQuery;
  }

  @override
  int get hashCode {
    return access_token.hashCode ^
        token_type.hashCode ^
        expires_in.hashCode ^
        refresh_token.hashCode ^
        accessQuery.hashCode;
  }
}

class AccessQuery {
  final Client client;
  final int user;
  AccessQuery({
    required this.client,
    this.user = 0,
  });

  AccessQuery copyWith({
    Client? client,
    int? user,
  }) {
    return AccessQuery(
      client: client ?? this.client,
      user: user ?? this.user,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'client': client.toMap(),
      'user': user,
    };
  }

  factory AccessQuery.fromMap(Map<String, dynamic> map) {
    return AccessQuery(
      client: Client.fromMap(map['client']),
      user: map['user']['id']?.toInt(),
    );
  }

  String toJson() => json.encode(toMap());

  factory AccessQuery.fromJson(String source) =>
      AccessQuery.fromMap(json.decode(source));

  @override
  String toString() => 'AccessQuery(client: $client, user: $user)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AccessQuery && other.client == client && other.user == user;
  }

  @override
  int get hashCode => client.hashCode ^ user.hashCode;
}

class Client {
  final String clientId;
  final List<String> grants;
  final List<String> redirectUris;
  Client({
    this.clientId = '',
    this.grants = const [],
    this.redirectUris = const [],
  });

  Client copyWith({
    String? clientId,
    List<String>? grants,
    List<String>? redirectUris,
  }) {
    return Client(
      clientId: clientId ?? this.clientId,
      grants: grants ?? this.grants,
      redirectUris: redirectUris ?? this.redirectUris,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'clientId': clientId,
      'grants': grants,
      'redirectUris': redirectUris,
    };
  }

  factory Client.fromMap(Map<String, dynamic> map) {
    return Client(
      clientId: map['clientId'],
      grants: List<String>.from(map['grants']),
      redirectUris: List<String>.from(map['redirectUris']),
    );
  }

  String toJson() => json.encode(toMap());

  factory Client.fromJson(String source) => Client.fromMap(json.decode(source));

  @override
  String toString() =>
      'Client(clientId: $clientId, grants: $grants, redirectUris: $redirectUris)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Client &&
        other.clientId == clientId &&
        listEquals(other.grants, grants) &&
        listEquals(other.redirectUris, redirectUris);
  }

  @override
  int get hashCode =>
      clientId.hashCode ^ grants.hashCode ^ redirectUris.hashCode;
}

class ClientSecret {}
