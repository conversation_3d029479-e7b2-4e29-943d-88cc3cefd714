import 'dart:convert';
import 'dart:developer';

import 'package:flutter/foundation.dart';

import 'dialog.dart';

class MEDAction {
  final String label;
  final String action;
  final String url;
  final bool validationChecks;
  final List<dynamic> buttonColor;
  final bool isBrightColor;

  MEDAction({
    this.label = '',
    this.action = '',
    this.url = '',
    this.buttonColor = const [38, 166, 154],
    this.isBrightColor = false,
    this.validationChecks = false,
  });

  MEDAction copyWith({
    String? label,
    String? action,
    String? url,
    List<int>? buttonColor,
    bool? isBrightColor,
  }) {
    return MEDAction(
      label: label ?? this.label,
      action: action ?? this.action,
      url: url ?? this.url,
      buttonColor: buttonColor ?? this.buttonColor,
      isBrightColor: isBrightColor ?? this.isBrightColor,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'label': label,
      'action': action,
      'url': url,
      'validationChecks': validationChecks,
      'buttonColor': buttonColor,
      'isBrightColor': isBrightColor,
    };
  }

  factory MEDAction.fromMap(Map<String, dynamic> map) {
    String debugLabel = map['label'] ?? '';
    if (debugLabel.contains('Logof')) {
      debugLabel = debugLabel.replaceAll('Logoff', 'Log off');
    }
    return MEDAction(
      label: debugLabel,
      action: map['action'] ?? '',
      url: map['url'] ?? '',
      validationChecks: map['validationChecks'] ?? false,
      buttonColor: map['buttonColor'] == null
          ? const [38, 166, 154]
          : map['buttonColor'].split(',').map((x) => int.parse(x)).toList(),
      isBrightColor: map['isBrightColor'] ?? false,
    );
  }

  String toJson() => json.encode(toMap());

  factory MEDAction.fromJson(String source) => MEDAction.fromMap(json.decode(source));

  @override
  String toString() {
    return 'MEDAction(label: $label, action: $action, url: $url, buttonColor: $buttonColor, isBrightColor: $isBrightColor)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MEDAction &&
        other.label == label &&
        other.action == action &&
        other.url == url &&
        listEquals(other.buttonColor, buttonColor) &&
        other.isBrightColor == isBrightColor;
  }

  @override
  int get hashCode {
    return label.hashCode ^
        action.hashCode ^
        url.hashCode ^
        buttonColor.hashCode ^
        isBrightColor.hashCode;
  }
}
