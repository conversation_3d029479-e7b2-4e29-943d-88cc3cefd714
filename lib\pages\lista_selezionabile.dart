import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:medarchiver_interfaces/interfaces.dart';

import '/core/models/dialog.dart' as d;
import '/widgets/global/med_button.dart';
import 'package:webviewx/webviewx.dart';

import '/core/models/action.dart';
import '/core/models/list_data.dart';

import '/widgets/global/action_button.dart';
import '/widgets/global/med_dialog.dart';
import '/widgets/global/med_html_widget.dart';
import '/widgets/MEDScaffold/header.dart';
import '/widgets/MEDScaffold/med_scaffold.dart';

class ListaSelezionabile extends StatefulWidget {
  final Map<String, dynamic> data;
  final Function(String, String, List<d.Dialog>) submit;
  const ListaSelezionabile({Key? key, required this.data, required this.submit}) : super(key: key);

  @override
  State<ListaSelezionabile> createState() => _ListaSelezionabileState();
}

class _ListaSelezionabileState extends State<ListaSelezionabile> {
  late final ListData data;
  @override
  void initState() {
    super.initState();
    data = ListData.fromMap(widget.data);
    IVoskPlugin voskPlugin = context.read<IVoskPlugin>();
    Map<String, AsyncCallback> commands = {};
    for (MEDAction action in data.actions) {
      commands.addAll({
        action.label.toLowerCase(): () async {
          widget.submit(action.action, selectedOption(), data.dialogs);
        }
      });
    }
    voskPlugin.setUserCommandsMapping = commands;
    voskPlugin.startWithGrammar(["[unk]"]);
  }

  @override
  Widget build(BuildContext context) {
    return MEDScaffold(
      header: Header(
        leadingIcon: data.header.leading.isEmpty ? null : Image.network(data.header.leading),
        title: HtmlWidget(data.header.title),
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          MedHtmlWidget(
              html: data.userInstructions,
              tags: const ['linkedaction'],
              callback: (val) => widget.submit(val, selectedOption(), data.dialogs)),
          Table(
              // columnWidths: const {
              //   0: FlexColumnWidth(.4),
              //   4: FlexColumnWidth(.4),
              // },
              defaultColumnWidth: const IntrinsicColumnWidth(),
              border: TableBorder.all(),
              children: [
                //titles
                TableRow(children: filterTitles(data)),
                //content
                for (var i = 0; i < data.table.rows.length; i++)
                  TableRow(children: [
                    if (!data.table.hiddenTitles.contains(0))
                      Center(
                        child: Checkbox(
                          value: data.table.rows[i].selected,
                          onChanged: (newVal) => checkBox(i, newVal),
                        ),
                      ),
                    if (!data.table.hiddenTitles.contains(1))
                      Center(child: Text(data.table.rows[i].progressivo)),
                    if (!data.table.hiddenTitles.contains(2))
                      Center(
                          child: data.table.rows[i].icon.isEmpty
                              ? null
                              : Image.network(
                                  data.table.rows[i].icon,
                                  width: 40,
                                )),
                    if (!data.table.hiddenTitles.contains(3))
                      Center(child: Text(data.table.rows[i].text)),
                    if (!data.table.hiddenTitles.contains(4))
                      Center(
                          child: data.table.rows[i].info.isEmpty
                              ? null
                              : IconButton(
                                  onPressed: () {
                                    String info = data.table.rows[i].info;
                                    if (info.startsWith('http')) {
                                      showDialog(
                                          context: context,
                                          builder: (_) => Dialog(
                                                child: WebViewX(
                                                  width: MediaQuery.of(context).size.width * .8,
                                                  height: MediaQuery.of(context).size.height * 7,
                                                  onWebViewCreated: (controller) {
                                                    controller.loadContent(info, SourceType.url);
                                                  },
                                                ),
                                              ));
                                    } else {
                                      showMEDdialog(
                                          context,
                                          d.Dialog(
                                              type: 'info',
                                              content: data.table.rows[i].info,
                                              buttons: [d.Button(label: 'OK', action: 'cancel')]));
                                    }
                                  },
                                  icon: const Icon(Icons.info_outline)))
                  ])
              ])
        ],
      ),
      bottom: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Wrap(
          alignment: WrapAlignment.spaceAround,
          spacing: MediaQuery.of(context).size.width * .05,
          runSpacing: 12,
          children: [
            for (MEDAction action in data.actions)
              ActionButton(
                action,
                onPressed: () => widget.submit(action.action, selectedOption(), data.dialogs),
              ),
          ],
        ),
      ),
    );
  }

  String selectedOption() {
    String result = '';
    for (var row in data.table.rows) {
      if (row.selected) {
        result += row.id + '|';
      }
    }
    return result;
  }

  checkBox(int i, bool? newVal) {
    if (!data.table.check) {
      for (var row in data.table.rows) {
        row.selected = false;
      }
    }
    data.table.rows[i].selected = newVal ?? false;
    setState(() {});
  }

  List<Widget> filterTitles(ListData data) {
    List<String> titles = List.from(data.table.titles);
    List<int> hiddenTitles = data.table.hiddenTitles;
    for (int hiddenTitle in hiddenTitles) {
      titles.removeAt(hiddenTitle);
    }
    List<Widget> widgets = titles
        .map((e) => Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                e,
                textAlign: TextAlign.center,
              ),
            ))
        .toList();
    return widgets;
  }
}
