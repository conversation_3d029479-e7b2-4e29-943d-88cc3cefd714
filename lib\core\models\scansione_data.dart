import 'dart:convert';

import 'package:flutter/foundation.dart';

import '/core/models/camera_info.dart';

import 'action.dart';
import 'dialog.dart';
import 'header.dart';

class ScansioneData {
  final Header header;
  final String userInstructions;
  final String userInstructionsLight;
  final CameraInfo cameraInfo;
  final String scannerType;
  String footerHtml;
  final List<MEDAction> actions;
  final List<Dialog> dialogs;
  ScansioneData({
    required this.header,
    this.userInstructions = '',
    this.userInstructionsLight = '',
    required this.cameraInfo,
    this.scannerType = '',
    this.footerHtml = '',
    this.actions = const [],
    this.dialogs = const [],
  });

  ScansioneData copyWith({
    Header? header,
    String? userInstructions,
    String? userInstructionsLight,
    CameraInfo? cameraInfo,
    String? scannerType,
    String? footerHtml,
    List<MEDAction>? actions,
  }) {
    return ScansioneData(
      header: header ?? this.header,
      userInstructions: userInstructions ?? this.userInstructions,
      userInstructionsLight:
          userInstructionsLight ?? this.userInstructionsLight,
      cameraInfo: cameraInfo ?? this.cameraInfo,
      scannerType: scannerType ?? this.scannerType,
      footerHtml: footerHtml ?? this.footerHtml,
      actions: actions ?? this.actions,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'header': header.toMap(),
      'userInstructions': userInstructions,
      'userInstructionsLight': userInstructionsLight,
      'cameraInfo': cameraInfo.toMap(),
      'scannerType': scannerType,
      'footerHtml': footerHtml,
      'actions': actions.map((x) => x.toMap()).toList(),
    };
  }

  factory ScansioneData.fromMap(Map<String, dynamic> map) {
    return ScansioneData(
      header: Header.fromMap(map['header']),
      userInstructions: map['userInstructions'] ?? '',
      userInstructionsLight: map['userInstructionsLight'] ?? '',
      cameraInfo: CameraInfo.fromMap(map['cameraInfo'] ?? {}),
      scannerType: map['scannerType'] ?? '',
      footerHtml: map['footerHtml'] ?? '',
      actions: List<MEDAction>.from(
          map['actions']?.map((x) => MEDAction.fromMap(x))),
      dialogs: List<Dialog>.from(
          map['dialogs']?.map((x) => Dialog.fromMap(x)) ?? []),
    );
  }

  String toJson() => json.encode(toMap());

  factory ScansioneData.fromJson(String source) =>
      ScansioneData.fromMap(json.decode(source));

  @override
  String toString() {
    return 'ScansioneData(header: $header, userInstructions: $userInstructions, userInstructionsLight: $userInstructionsLight, cameraInfo: $cameraInfo, scannerType: $scannerType, footerHtml: $footerHtml, actions: $actions)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ScansioneData &&
        other.header == header &&
        other.userInstructions == userInstructions &&
        other.userInstructionsLight == userInstructionsLight &&
        other.cameraInfo == cameraInfo &&
        other.scannerType == scannerType &&
        other.footerHtml == footerHtml &&
        listEquals(other.actions, actions);
  }

  @override
  int get hashCode {
    return header.hashCode ^
        userInstructions.hashCode ^
        userInstructionsLight.hashCode ^
        cameraInfo.hashCode ^
        scannerType.hashCode ^
        footerHtml.hashCode ^
        actions.hashCode;
  }
}
