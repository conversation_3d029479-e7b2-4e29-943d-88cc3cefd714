import 'dart:convert';

class Header {
  final String leading;
  final String title;
  Header({
    this.leading = '',
    this.title = '',
  });

  Header copyWith({
    String? leading,
    String? title,
  }) {
    return Header(
      leading: leading ?? this.leading,
      title: title ?? this.title,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'leading': leading,
      'title': title,
    };
  }

  factory Header.fromMap(Map<String, dynamic> map) {
    return Header(
      leading: map['leading'] ?? '',
      title: map['title'],
    );
  }

  String toJson() => json.encode(toMap());

  factory Header.fromJson(String source) => Header.fromMap(json.decode(source));

  @override
  String toString() => 'Header(leading: $leading, title: $title)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Header && other.leading == leading && other.title == title;
  }

  @override
  int get hashCode => leading.hashCode ^ title.hashCode;
}
