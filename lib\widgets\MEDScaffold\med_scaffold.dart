import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:medarchiver_common/services.dart';
import 'package:medarchiver_interfaces/interfaces.dart';

import 'package:null_vosk_plugin/null_vosk_plugin.dart';

class MEDScaffold extends StatelessWidget {
  final Widget? header;
  final Widget? bottom;
  final Widget? body;
  final Color backgroundcolor;
  final ValueListenable<bool> _dispatchIsActive = serviceLocator<IVoskPlugin>().dispatchIsActive;
  MEDScaffold({
    Key? key,
    this.header,
    this.bottom,
    this.body,
    this.backgroundcolor = Colors.white,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: [
          ValueListenableBuilder(
              valueListenable: _dispatchIsActive,
              builder: (BuildContext context, bool status, Widget? child) {
                return status
                    ? Icon(
                        Icons.mic_outlined,
                        color: Colors.greenAccent,
                        size: 32,
                      )
                    : Icon(
                        Icons.mic_off_outlined,
                        color: Colors.redAccent,
                        size: 32,
                      );
              })
        ],
      ),
      backgroundColor: backgroundcolor,
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (header != null) header!,
            if (body != null) Expanded(child: SingleChildScrollView(child: body!)),
            if (bottom != null) bottom!,
          ],
        ),
      ),
    );
  }
}
