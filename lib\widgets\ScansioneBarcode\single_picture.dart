import 'dart:convert';
import 'dart:developer';

import 'package:camera/camera.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:medarchiver_common/interfaces.dart';
import 'package:medarchiver_common/models.dart';
import 'package:medarchiver_common/services.dart';

class SinglePicture extends StatefulWidget {
  final Size size;
  const SinglePicture({Key? key, required this.size}) : super(key: key);

  @override
  _SinglePictureState createState() => _SinglePictureState();
}

class _SinglePictureState extends State<SinglePicture> {
  late CameraController controller;
  List<CameraDescription> cameras = [];
  bool isCameraInitialized = false;
  bool photoIsLoading = false;
  final IKeyValueStorage appData = serviceLocator<IKeyValueStorage>();
  @override
  void initState() {
    initCamera();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Size size = widget.size;
    return Stack(
      alignment: AlignmentDirectional.bottomCenter,
      children: [
        SizedBox(
          height: size.height,
          width: size.width,
          child: cameras.isNotEmpty
              ? ClipRect(
                  child: OverflowBox(
                    alignment: Alignment.center,
                    child: FittedBox(
                      fit: BoxFit.fitWidth,
                      child: SizedBox(
                        width: size.width,
                        child: CameraPreview(
                          controller,
                        ),
                      ),
                    ),
                  ),
                )
              : const Center(
                  child: CircularProgressIndicator(),
                ),
        ),
        Positioned(
          bottom: 12,
          child: FloatingActionButton(
            elevation: 2,
            onPressed: () async {
              if (photoIsLoading) {
                return;
              }
              setState(() {
                photoIsLoading = true;
              });
              controller.setFlashMode(FlashMode.off);
              XFile pic = await controller.takePicture();
              String base64 = base64Encode(await pic.readAsBytes());
              final String apiUrl = AllAppDataValues().apiUrl.replaceAll('V1', 'V2');
              try {
                final res = await Dio().post(
                  '$apiUrl/saveImage',
                  data: {
                    'image': base64,
                    'deviceId': appData["device_id"],
                    'processId': appData['notificationId'],
                  },
                );
                //processId=pincopallino
                await Fluttertoast.showToast(msg: "Immagine acquisita con successo!", gravity: ToastGravity.CENTER);
              } catch (exception) {
                log("$exception");
                await Fluttertoast.showToast(msg: "ERRORE: Immagine NON acquisita!", gravity: ToastGravity.CENTER);
              }
              setState(() {
                photoIsLoading = false;
              });
            },
            child: photoIsLoading
                ? const CircularProgressIndicator(
                    color: Colors.white,
                  )
                : const Icon(
                    Icons.camera_alt,
                    color: Colors.white,
                  ),
          ),
        ),
      ],
    );
  }

  void initCamera() async {
    cameras = await availableCameras();
    controller = CameraController(cameras[0], ResolutionPreset.max);
    await controller.initialize();
    if (!mounted) return;
    setState(() {
      isCameraInitialized = true;
    });
  }
}
