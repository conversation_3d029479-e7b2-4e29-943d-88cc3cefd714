import 'dart:convert';

import 'package:flutter/foundation.dart';

import 'action.dart';
import 'dialog.dart';
import 'header.dart';
import 'text_input.dart';

class ReviewData {
  final String type;
  final Header header;
  final String htmlElement;
  final List<TextInput> textInputs;
  final List<MEDAction> actions;
  final List<Dialog> dialogs;
  ReviewData(
      {this.type = '',
      required this.header,
      this.htmlElement = '',
      this.textInputs = const [],
      this.actions = const [],
      this.dialogs = const []});

  ReviewData copyWith({
    String? type,
    Header? header,
    String? htmlElement,
    List<TextInput>? textInputs,
    List<MEDAction>? actions,
  }) {
    return ReviewData(
      type: type ?? this.type,
      header: header ?? this.header,
      htmlElement: htmlElement ?? this.htmlElement,
      textInputs: textInputs ?? this.textInputs,
      actions: actions ?? this.actions,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'type': type,
      'header': header.toMap(),
      'htmlElement': htmlElement,
      'textInputs': textInputs.map((x) => x.toMap()).toList(),
      'actions': actions.map((x) => x.toMap()).toList(),
    };
  }

  factory ReviewData.fromMap(Map<String, dynamic> map) {
    return ReviewData(
      type: map['type'],
      header: Header.fromMap(map['header']),
      htmlElement: map['htmlElement'],
      textInputs: map['textInputs'] == null
          ? []
          : List<TextInput>.from(map['textInputs']?.map((x) => TextInput.fromMap(x))),
      actions: List<MEDAction>.from(map['actions']?.map((x) => MEDAction.fromMap(x))),
      dialogs: List<Dialog>.from(map['dialogs']?.map((x) => Dialog.fromMap(x)) ?? []),
    );
  }

  String toJson() => json.encode(toMap());

  factory ReviewData.fromJson(String source) => ReviewData.fromMap(json.decode(source));

  @override
  String toString() {
    return 'ReviewData(type: $type, header: $header, htmlElement: $htmlElement, textInputs: $textInputs, actions: $actions)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ReviewData &&
        other.type == type &&
        other.header == header &&
        other.htmlElement == htmlElement &&
        listEquals(other.textInputs, textInputs) &&
        listEquals(other.actions, actions);
  }

  @override
  int get hashCode {
    return type.hashCode ^
        header.hashCode ^
        htmlElement.hashCode ^
        textInputs.hashCode ^
        actions.hashCode;
  }
}
