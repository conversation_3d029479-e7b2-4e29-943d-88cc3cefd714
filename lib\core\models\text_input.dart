import 'dart:convert';

import 'package:flutter/widgets.dart';

import 'validation_data.dart';

import 'validation_error.dart';

class TextInput {
  final String type;
  final String id;
  final TextEditingController controller;
  final bool hidden;
  final String label;
  bool hasError;
  String error;
  final ValidationData validationData;
  final ValidationError validationError;
  TextInput({
    this.type = '',
    this.id = '',
    required this.controller,
    this.hidden = false,
    this.label = '',
    this.hasError = false,
    this.error = '',
    required this.validationData,
    required this.validationError,
  });

  TextInput copyWith({
    String? type,
    String? id,
    TextEditingController? controller,
    bool? hidden,
    String? label,
    bool? hasError,
    String? error,
    ValidationData? validationData,
    ValidationError? validationError,
  }) {
    return TextInput(
      type: type ?? this.type,
      id: id ?? this.id,
      controller: controller ?? this.controller,
      hidden: hidden ?? this.hidden,
      label: label ?? this.label,
      hasError: hasError ?? this.hasError,
      error: error ?? this.error,
      validationData: validationData ?? this.validationData,
      validationError: validationError ?? this.validationError,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'type': type,
      'id': id,
      'controller': controller.text,
      'hidden': hidden,
      'label': label,
      'hasError': hasError,
      'error': error,
      'validationData': validationData.toMap(),
    };
  }

  factory TextInput.fromMap(Map<String, dynamic> map) {
    return TextInput(
        type: map['type'] ?? '',
        id: map['id'].toString(),
        controller: TextEditingController(text: map['value'] ?? ''),
        hidden: map['hidden'] ?? false,
        label: map['label'] ?? '',
        hasError: map['hasError'] ?? false,
        error: map['error'] ?? '',
        validationData: ValidationData.fromMap(map['validationData'] ?? {}),
        validationError: ValidationError.fromMap(map['validationErrors'] ?? {}));
  }

  String toJson() => json.encode(toMap());

  factory TextInput.fromJson(String source) => TextInput.fromMap(json.decode(source));

  @override
  String toString() {
    return 'TextInput(type: $type, id: $id, controller: $controller, hidden: $hidden, label: $label, hasError: $hasError, error: $error, validationData: $validationData)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TextInput &&
        other.type == type &&
        other.id == id &&
        other.controller == controller &&
        other.hidden == hidden &&
        other.label == label &&
        other.hasError == hasError &&
        other.error == error &&
        other.validationData == validationData;
  }

  @override
  int get hashCode {
    return type.hashCode ^
        id.hashCode ^
        controller.hashCode ^
        hidden.hashCode ^
        label.hashCode ^
        hasError.hashCode ^
        error.hashCode ^
        validationData.hashCode;
  }
}
