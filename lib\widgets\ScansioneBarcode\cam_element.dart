import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medarchiver_common/interfaces.dart';
import 'package:medarchiver_common/services.dart';
import '/core/core.dart';
import '/core/models/camera_info.dart';
import '/widgets/ScansioneBarcode/object_recognition.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'barcode_scanner.dart';
import 'single_picture.dart';

class CamElement extends StatefulWidget {
  final String type;
  final String? objRecognitionUrl;
  final CameraInfo cameraInfo;
  final String cameraInstructions;
  final String autoCommitAction;
  final Function(String) onBarcodeFound;
  const CamElement(this.type,
      {Key? key,
      required this.cameraInfo,
      required this.autoCommitAction,
      required this.onBarcodeFound,
      this.objRecognitionUrl,
      this.cameraInstructions = ''})
      : super(key: key);

  @override
  State<CamElement> createState() => _CamElementState();
}

class _CamElementState extends State<CamElement> {
  bool userStartedInteraction = false;
  late String pageNumber;
  late String notificationId;

  @override
  void initState() {
    userStartedInteraction = widget.cameraInfo.cameraAuto;
    //Future.delayed(Duration.zero, () async {
    //  SharedPreferences spm = await SharedPreferences.getInstance();
    //  //pageNumber = spm.getString('pageNumber') ?? '';
    //  notificationId = spm.getString('notificationId') ?? '';
    //});
    pageNumber = serviceLocator<IKeyValueStorage>()["pageNumber"];
    notificationId = serviceLocator<IKeyValueStorage>()["notificationId"];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
      PageCubit cubit = BlocProvider.of<PageCubit>(context, listen: false);

      final Size widgetSize = Size(min(constraints.maxWidth, 600),
          min(MediaQuery.of(context).size.height * .8, 300));
      if (userStartedInteraction) {
        switch (widget.type) {
          case 'barcode':
            return BarcodeScanner(
              size: widgetSize,
              cameraId: widget.cameraInfo.cameraType,
              onBarcodeFound: (barcode) {
                setState(() {
                  userStartedInteraction = false;
                  widget.onBarcodeFound(barcode);
                });
              },
            );
          case 'objectRecognition':
            // return ObjectRecognition(size: widgetSize);
            Future.delayed(Duration.zero, () {
              showDialog(
                  context: context,
                  builder: (_) => ObjectRecognition(
                        text: widget.cameraInstructions,
                        url: widget.objRecognitionUrl ?? '',
                        cameraId: widget.cameraInfo.cameraType,
                        onValid: (status) async {
                          cubit.postPage(widget.autoCommitAction, status,
                              notificationId, pageNumber, context, const []);
                        },
                      ),
                  useSafeArea: false,
                  barrierDismissible: false);
            });

            break;
          case 'singlePicture':
            return SinglePicture(size: widgetSize);
        }
        userStartedInteraction = false;
      }

      /// User didn't start interaction
      late final IconData icon;
      late final String text;
      switch (widget.type) {
        case 'barcode':
          text = 'Tap to scan';
          icon = Icons.qr_code;
          break;
        case 'singlePicture':
          text = 'Tap to open the camera';
          icon = Icons.camera_alt;
          break;
        case 'objectRecognition':
          text = 'Tap to recognize objects';
          icon = Icons.visibility;
          break;
      }
      return InkWell(
        onTap: () {
          setState(() {
            userStartedInteraction = true;
          });
        },
        child: Container(
          color: Colors.grey.shade200,
          height: widgetSize.height,
          width: widgetSize.width,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    icon,
                    size: 48,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Text(text),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }
}
