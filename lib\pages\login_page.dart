import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';

import 'package:medarchiver_common/interfaces.dart';
import 'package:medarchiver_common/models.dart';
import 'package:medarchiver_common/services.dart';
import 'package:encrypt/encrypt.dart' as e;

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  ILogger logger = serviceLocator<ILogger>()..baseClassName = 'LoginPage';
  late final e.Encrypter encrypter;

  @override
  void initState() {
    super.initState();
    encrypter = e.Encrypter(
      e.AES(
        e.Key.fromUtf8(
          const String.fromEnvironment(
            'loginKey',
            defaultValue: 'lhLb0zLJN2GBMZKGLJz6ikIf1e0WqB64',
          ),
        ),
      ),
    );
    initAsync(context);
  }

  Future initAsync(BuildContext context) async {
    String testEncrypt =
        encrypter.encrypt('{"user": "oncologia", "pw": "dubai"}', iv: e.IV.fromLength(16)).base64;
    logger.fine(
      "Test encrypt: $testEncrypt",
      className: 'LoginPage',
      methodName: 'initState',
    );
    IKeyValueStorage appData = serviceLocator<IKeyValueStorage>();

    IOAuth2Api oauth = serviceLocator<IOAuth2Api>();
    await oauth.createDevice(appData["device_id"]);

    //#region process extra data
    try {
      logger.debug("Decoding extras values.", methodName: 'initAsync');
      String extra = serviceLocator<IKeyValueStorage>()["extra"];
      if (extra.isNotEmpty) {
        Map<String, dynamic> extraMap = json.decode(extra);
        appData["username"] = extraMap["username"];
        appData["password"] = extraMap["password"];
        appData["notificationId"] = extraMap["notificationId"].toString();
      }
    } catch (e) {
      logger.error("LoginPage: Extra values not founds. $e.");
    }
    //#endregion
//TODO: Rimuovere questa logica HARDCODED...
    String loginUser = appData['username'];
    if (loginUser.isEmpty) {
      loginUser = "oncologia";
    }
    String loginPass = appData['password'];
    if (loginPass.isEmpty) {
      loginPass = "dubai";
    }
    bool hasLogin = await oauth.login(loginUser, loginPass);
    String? notificationPath = await getNotificationPath();
    // if (notificationPath?.endsWith('/') == true) {
    //   notificationPath = notificationPath!.substring(0, notificationPath.length - 1);
    // }
    if (hasLogin) {
      if (notificationPath != null && notificationPath.isNotEmpty) {
        Navigator.of(context).pushReplacementNamed(notificationPath);
        return;
      }
      String notificationId = appData["notificationId"];
      if (notificationId.isEmpty) notificationId = "-17";
      String pageNumber = appData["pageNumber"];
      if (pageNumber.isEmpty) pageNumber = "1";
      await Navigator.of(context).pushReplacementNamed('/preparazione/$notificationId/$pageNumber');
    }
  }

  Future<String?> getNotificationPath() async {
    RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    FirebaseMessaging.onMessageOpenedApp.listen((event) {
      initialMessage = event;
    });
    Future.delayed(const Duration(milliseconds: 100));
    if (initialMessage != null) {
      return initialMessage!.data['path'];
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
