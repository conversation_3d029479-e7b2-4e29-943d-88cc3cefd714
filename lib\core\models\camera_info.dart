import 'dart:convert';

class CameraInfo {
  final String cameraSize;
  final int cameraType;
  final bool cameraAuto;
  CameraInfo({
    this.cameraSize = '',
    this.cameraType = 0,
    this.cameraAuto = false,
  });

  CameraInfo copyWith({
    String? cameraSize,
    int? cameraType,
    bool? cameraAuto,
  }) {
    return CameraInfo(
      cameraSize: cameraSize ?? this.cameraSize,
      cameraType: cameraType ?? this.cameraType,
      cameraAuto: cameraAuto ?? this.cameraAuto,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'cameraSize': cameraSize,
      'cameraType': cameraType,
      'cameraAuto': cameraAuto,
    };
  }

  factory CameraInfo.fromMap(Map<String, dynamic> map) {
    return CameraInfo(
      cameraSize: map['cameraSize'] ?? '',
      cameraType: map['cameraType']?.toInt() ?? 0,
      cameraAuto: map['cameraAuto'] ?? false,
    );
  }

  String toJson() => json.encode(toMap());

  factory CameraInfo.fromJson(String source) => CameraInfo.fromMap(json.decode(source));

  @override
  String toString() =>
      'CameraInfo(cameraSize: $cameraSize, cameraType: $cameraType, cameraAuto: $cameraAuto)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is CameraInfo &&
        other.cameraSize == cameraSize &&
        other.cameraType == cameraType &&
        other.cameraAuto == cameraAuto;
  }

  @override
  int get hashCode => cameraSize.hashCode ^ cameraType.hashCode ^ cameraAuto.hashCode;
}
