import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:medarchiver_common/interfaces.dart';
import 'package:medarchiver_common/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HTTPQuery {
  Future<HTTPResponse> request(String method, String url,
      {Map<String, dynamic>? data,
      Map<String, dynamic> optionalHeaders = const {},
      bool useToken = false}) async {
    Map<String, dynamic> headers = {
      'Authorization': 'Bearer ',
    };
    if (useToken) {
      //SharedPreferences sp = await SharedPreferences.getInstance();
      //String token = sp.getString('accessToken') ?? '';
      String token = serviceLocator<IKeyValueStorage>()["access_token"];
      log('access token: ' + token);
      headers['Authorization'] = 'Bearer $token';
    }
    try {
      Response res = await Dio(BaseOptions(connectTimeout: 20 * 1000)).request(
        url,
        options: Options(
          method: method,
          headers: headers,
          //followRedirects: true,
          //contentType: encoded ? Headers.formUrlEncodedContentType : null,
        ),
        data: data,
      );
      if (res.statusCode != 200 && res.statusCode != 201) {
        throw (DioError(response: res, requestOptions: RequestOptions(path: "")));
      }
      return HTTPResponse(statusCode: res.statusCode!, hasData: true, data: res.data);
    } catch (err) {
      //log.e(err.toString());
      DioError e = err as DioError;
      Response res = e.response ??
          Response(requestOptions: RequestOptions(path: ''), data: e.message, statusCode: 0);
      HTTPResponse errResp = HTTPResponse(
        statusCode: res.statusCode!,
        hasError: true,
        error: res.data,
      );
      errResp.printError();
      return errResp;
    }
  }
}

class HTTPResponse {
  late int statusCode;
  dynamic error;
  late bool hasError;
  dynamic data;
  late bool hasData;

  void printError() {
    print(statusCode.toString() + ': ' + error.toString());
  }

  void printData() {
    print(statusCode.toString() + ': ' + data.toString());
  }

  HTTPResponse(
      {required this.statusCode,
      this.error,
      this.hasError = false,
      this.hasData = false,
      this.data});
}
