import 'package:flutter/material.dart';
import 'package:medarchiver_common/interfaces.dart';
import 'package:medarchiver_common/pages.dart';
import 'package:medarchiver_common/services.dart';
import 'package:preparazione_assistita_farmaci/pages/videocall.dart';
import 'pages/login_page.dart' as MyLoginPage;
import 'pages/preparazione_builder.dart';

Route<dynamic> router(RouteSettings settings) {
  List<String> url = settings.name?.split('/').sublist(1) ?? [];
  ILogger logger = serviceLocator<ILogger>();
  logger.debug(
    "Router Settings Name:${settings.name}",
    className: 'router',
    methodName: 'router',
  );
  String screenName = url[0];

  //InitializeState currStatus = serviceLocator<IInitializeViewModel>().initState.value;
  if (serviceLocator<IInitializeViewModel>().initState.value != InitializeState.CONFIG_READY) {
    screenName = "startpage";
  }

  switch (screenName) {
    case "preparazione":
      String notificationId = url.length == 3 ? url[1] : "";
      String pageNumber = url.length == 3 ? url[2] : "";
      serviceLocator<IKeyValueStorage>()["notificationId"] = notificationId; //url[1];
      serviceLocator<IKeyValueStorage>()["pageNumber"] = pageNumber; //url[2];
      serviceLocator<IKeyValueStorage>()["isNewPage"] = "true";
      return MaterialPageRoute(
        builder: (_) => PreparazioneBuilder(
          notificationId: notificationId,
          pageNumber: pageNumber,
          isNewPage: true,
        ),
        settings: settings,
      );
    case "login":
      return MaterialPageRoute(
        builder: (_) => MyLoginPage.LoginPage(),
        settings: const RouteSettings(name: '/login'),
      );
    case "startpage":
      logger.debug(
        "case: startpage",
        className: 'router',
        methodName: 'router',
      );
      return MaterialPageRoute(
        builder: (context) => StartPage(
          useRouter: true,
        ),
        settings: const RouteSettings(name: '/startpage'),
      );
    case "videocall":
      logger.debug(
        "case: startpage",
        className: 'router',
        methodName: 'router',
      );
      return MaterialPageRoute(
        builder: (context) => Videocall(settings.name ?? ''),
        settings: RouteSettings(name: settings.name),
      );
    default:
      return MaterialPageRoute(
        builder: (_) => const UnknownRoute(),
        settings: const RouteSettings(name: '404'),
      );
  }

  // root
  /*if (url.length == 1 && url[0].isEmpty) {
    return MaterialPageRoute(
      builder: (_) => const LoginPage(),
      settings: const RouteSettings(name: '/'),
    );
  }*/
  if (url.length == 3 && url[0] == 'preparazione') {
    //SMRACH: provo a salvare i parametri su storage..
    serviceLocator<IKeyValueStorage>()["notificationId"] = url[1];
    serviceLocator<IKeyValueStorage>()["pageNumber"] = url[2];
    serviceLocator<IKeyValueStorage>()["isNewPage"] = "true";
    return MaterialPageRoute(
      builder: (_) => PreparazioneBuilder(
        notificationId: url[1],
        pageNumber: url[2],
        isNewPage: true,
      ),
      settings: settings,
    );
  }
}

class UnknownRoute extends StatelessWidget {
  const UnknownRoute({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Text('404'),
      ),
    );
  }
}
