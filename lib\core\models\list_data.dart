import 'dart:convert';

import 'package:flutter/foundation.dart';
import '/core/models/dialog.dart';

import 'action.dart';
import 'header.dart';
import 'table.dart';

class ListData {
  final Header header;
  final String userInstructions;
  final Table table;
  final List<MEDAction> actions;
  final List<Dialog> dialogs;
  ListData({
    required this.header,
    this.userInstructions = '',
    required this.table,
    this.actions = const [],
    this.dialogs = const [],
  });

  ListData copyWith({
    Header? header,
    String? userInstructions,
    Table? table,
    List<MEDAction>? actions,
  }) {
    return ListData(
      header: header ?? this.header,
      userInstructions: userInstructions ?? this.userInstructions,
      table: table ?? this.table,
      actions: actions ?? this.actions,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'header': header.toMap(),
      'userInstructions': userInstructions,
      'table': table.toMap(),
      'actions': actions.map((x) => x.toMap()).toList(),
    };
  }

  factory ListData.fromMap(Map<String, dynamic> map) {
    return ListData(
      header: Header.fromMap(map['header']),
      userInstructions: map['userInstructions'].toString(),
      table: Table.fromMap(map['table']),
      actions: List<MEDAction>.from(map['actions']?.map((x) => MEDAction.fromMap(x))),
      dialogs: List<Dialog>.from(map['dialogs']?.map((x) => Dialog.fromMap(x)) ?? []),
    );
  }

  String toJson() => json.encode(toMap());

  factory ListData.fromJson(String source) => ListData.fromMap(json.decode(source));

  @override
  String toString() {
    return 'ListData(header: $header, userInstructions: $userInstructions, table: $table, actions: $actions)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ListData &&
        other.header == header &&
        other.userInstructions == userInstructions &&
        other.table == table &&
        listEquals(other.actions, actions);
  }

  @override
  int get hashCode {
    return header.hashCode ^ userInstructions.hashCode ^ table.hashCode ^ actions.hashCode;
  }
}
