import 'dart:async';
import 'dart:developer';
import 'package:flutter/cupertino.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:medarchiver_common/interfaces.dart';
import 'package:medarchiver_common/services.dart';
import '/core/check_dialog.dart';
import '/core/models/dialog.dart';

import '/core/http_client.dart';
import '/core/parse_oracle_error.dart';
import '/core/models/page.dart';
import '/widgets/global/med_dialog.dart';

import '/services/services.dart';

class PageCubit extends Cubit<MEDPage> {
  PageCubit() : super(PageEmpty());

  getPage(String notificationId, String pageNumber) async {
    //La metto quà in questo modo, cosi viene valutata ogni volta.
    IKeyValueStorage appData = serviceLocator<IKeyValueStorage>();
    String apiUrl = appData["API_URL"];

    emit(PageLoading());
    //TODO: risolvere il problema per cui lo stato PageLoading non viene emesso senza delay
    //https://github.com/felangel/bloc/issues/173#issuecomment-478279105
    await Future.delayed(const Duration(milliseconds: 500), () {});
    //sp.setString('pageNumber', pageNumber);
    //sp.setString('notificationId', notificationId);

    appData['pageNumber'] = pageNumber;
    appData['notificationId'] = notificationId;

    HTTPResponse res = await HTTPQuery().request(
      'GET',
      '$apiUrl/$notificationId/$pageNumber',
      useToken: true,
    );
    if (res.hasError) {
      emit(PageError(error: res.error.toString()));
      return;
    }
    PageLoaded page = PageLoaded(type: res.data['data']['page']['type']);
    page.data = res.data['data']['page'];
    logger.debug(
      "Response Page Data:" + res.data['data']['page'].toString(),
      className: 'PageCubit',
      methodName: 'getPage',
    );
    emit(page);
  }

  postPage(
    String event,
    String selectedOptions,
    String notificationId,
    String pageNumber,
    BuildContext context,
    List<Dialog> dialogs,
  ) async {
    //La metto quà in questo modo, cosi viene valutata ogni volta.
    IKeyValueStorage appData = serviceLocator<IKeyValueStorage>();
    String apiUrl = appData["API_URL"];

    if (await checkDialogs(context, event, dialogs, 'beforeAction')) {
      return;
    }

    //SMRACH: Credo sarebbe meglio cambiare logica waiting.
    showLoadingDialog(context);

    Map<String, dynamic> data = {
      'selectedAction': event,
      'selectedOption': selectedOptions,
      'comment': '',
    };
    logger.debug(
      "postPage: " + data.toString(),
      className: 'PageCubit',
      methodName: 'postPage',
    );
    HTTPResponse res = await HTTPQuery().request(
      'POST',
      '$apiUrl/$notificationId/$pageNumber',
      useToken: true,
      data: data,
    );
    //SMRACH: questo pop serve per chiudere il dialog?
    Navigator.pop(context);
    if (res.hasError) {
      logger.error(
        res.error.toString(),
        className: 'PageCubit',
        methodName: 'postPage',
      );
      if (res.error is String) {
        await showMEDdialog(context,
            Dialog(type: 'error', content: res.error, buttons: [Button(label: 'OK', action: '')]));
      } else {
        await showMEDdialog(
            context,
            Dialog(
                type: 'error',
                content: parseOracleError(res.error['message'].toString()),
                buttons: [Button(label: 'OK', action: '')]));
      }
      return;
    }
    logger.debug(
      res.data.toString(),
      className: 'PageCubit',
      methodName: 'postPage',
    );
    //emit(PageEmpty());
    await Navigator.pushReplacementNamed(
      context,
      "/preparazione/$notificationId/${res.data['data']['pageId']}",
    );
    return;
  }
}
