import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:preparazione_assistita_farmaci/widgets/global/pictures_widget.dart';

class MedHtmlWidget extends StatelessWidget {
  final String html;
  final List<String> tags;
  final Function(String) callback;
  const MedHtmlWidget({required this.html, this.tags = const [], required this.callback, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HtmlWidget(
      html,
      customWidgetBuilder: (element) {
        for (String tag in tags) {
          if (element.attributes.containsKey(tag)) {
            return GestureDetector(
              child: HtmlWidget(element.innerHtml),
              onTap: () {
                callback(element.attributes[tag].toString());
              },
            );
          }
          if (element.localName == 'imagecapture') {
            final int n = int.tryParse(element.attributes['number'] ?? '1') ?? 1;
            final String label = element.attributes['label'] ?? '';
            return Center(
              child: ElevatedButton(
                  child: Text(label),
                  onPressed: () => showDialog(
                        context: context,
                        builder: (_) => PicturesDialog(n),
                      )),
            );
          }
        }
      },
    );
  }
}
