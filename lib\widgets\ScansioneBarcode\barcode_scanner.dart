import 'package:flutter/material.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class BarcodeScanner extends StatefulWidget {
  final Size size;
  final Function(String) onBarcodeFound;
  final int cameraId;
  const BarcodeScanner({Key? key, required this.size, required this.onBarcodeFound, this.cameraId = 0}) : super(key: key);

  @override
  _BarcodeScannerState createState() => _BarcodeScannerState();
}

class _BarcodeScannerState extends State<BarcodeScanner> {
  @override
  Widget build(BuildContext context) {
    Size size = widget.size;
    return SizedBox(
      width: size.width,
      height: size.height,
      child: Stack(
        children: [
          QRView(
            cameraFacing: widget.cameraId == 0 ? CameraFacing.back : CameraFacing.front,
            key: GlobalKey(),
            onQRViewCreated: (QRViewController controller) {
              controller.scannedDataStream.listen((element) => widget.onBarcodeFound("${element.code}"));
            },
            // overlay: QrScannerOverlayShape(
            //   borderColor: Colors.red,
            //   borderWidth: 75,
            //   // borderLength: 0,
            // ),
          ),
        ],
      ),
    );
  }
}
