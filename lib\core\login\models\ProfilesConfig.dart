import 'dart:convert';

import 'package:flutter/foundation.dart';

class ProfilesConfig {
  final String forceNewProfile;
  final List<Profile> profiles;
  ProfilesConfig({
    this.forceNewProfile = '',
    this.profiles = const [],
  });

  ProfilesConfig copyWith({
    String? forceNewProfile,
    List<Profile>? profiles,
  }) {
    return ProfilesConfig(
      forceNewProfile: forceNewProfile ?? this.forceNewProfile,
      profiles: profiles ?? this.profiles,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'forceNewProfile': forceNewProfile,
      'profiles': profiles.map((x) => x.toMap()).toList(),
    };
  }

  factory ProfilesConfig.fromMap(Map<String, dynamic> map) {
    return ProfilesConfig(
      forceNewProfile: map['forceNewProfile'],
      profiles:
          List<Profile>.from(map['profiles']?.map((x) => Profile.fromMap(x))),
    );
  }

  String toJson() => json.encode(toMap());

  factory ProfilesConfig.fromJson(String source) =>
      ProfilesConfig.fromMap(json.decode(source));

  @override
  String toString() =>
      'ProfilesConfig(forceNewProfile: $forceNewProfile, profiles: $profiles)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ProfilesConfig &&
        other.forceNewProfile == forceNewProfile &&
        listEquals(other.profiles, profiles);
  }

  @override
  int get hashCode => forceNewProfile.hashCode ^ profiles.hashCode;
}

class Profile {
  final int id;
  final String name;
  Profile({
    this.id = 0,
    this.name = '',
  });

  Profile copyWith({
    int? id,
    String? name,
  }) {
    return Profile(
      id: id ?? this.id,
      name: name ?? this.name,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory Profile.fromMap(Map<String, dynamic> map) {
    return Profile(
      id: map['id']?.toInt(),
      name: map['name'],
    );
  }

  String toJson() => json.encode(toMap());

  factory Profile.fromJson(String source) =>
      Profile.fromMap(json.decode(source));

  @override
  String toString() => 'Profile(id: $id, name: $name)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Profile && other.id == id && other.name == name;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}
