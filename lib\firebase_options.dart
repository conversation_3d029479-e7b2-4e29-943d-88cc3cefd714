// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDV5_tEn7Gcjvp3ANG2JQTyfcN-BG-5vxo',
    appId: '1:1051656954599:web:b3249f6df5505754ae3842',
    messagingSenderId: '1051656954599',
    projectId: 'universal-frontend',
    authDomain: 'universal-frontend.firebaseapp.com',
    storageBucket: 'universal-frontend.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBeSCPh3qpXi014I4vQzqWM4-pgorFW9mU',
    appId: '1:1051656954599:android:731383cef08940edae3842',
    messagingSenderId: '1051656954599',
    projectId: 'universal-frontend',
    storageBucket: 'universal-frontend.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDPDFPeCNwi1cVm6NG_hrZObu8ONDUSVU4',
    appId: '1:1051656954599:ios:139240f82ee25c69ae3842',
    messagingSenderId: '1051656954599',
    projectId: 'universal-frontend',
    storageBucket: 'universal-frontend.appspot.com',
    iosClientId: '1051656954599-vijqnkl282mn2q7buibge022ghpdsar5.apps.googleusercontent.com',
    iosBundleId: 'com.example.preparazioneAssistitaFarmaci',
  );
}
